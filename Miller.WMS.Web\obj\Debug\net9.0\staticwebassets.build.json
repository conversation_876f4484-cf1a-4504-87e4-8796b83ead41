{"Version": 1, "Hash": "J5LrmcOT3QZzsjo+ctGtnXY5xDVGlJEFk3F0cIiLWJg=", "Source": "Miller.WMS.Web", "BasePath": "_content/Miller.WMS.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Miller.WMS.Web\\wwwroot", "Source": "Miller.WMS.Web", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "Pattern": "**"}], "Assets": [{"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\1bpdowd3ur-n5tfi6zt97.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=n5tfi6zt97}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fd8at7e5rf", "Integrity": "Hd51/ICuZDXnORaSusIoHCgUhhYiqlw+xpRZYL70fuY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 28200, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\1y5rswsd3k-r4e9w2rdcm.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21g01g2f66", "Integrity": "JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44266, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\2aijt2o6r4-eve9uzuztn.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=eve9uzuztn}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "43czolro0z", "Integrity": "EJxn9g8lywGEK14NsSRZd2K3+UXEihxQuQQ9RrZuoJM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56387, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\3wy4uav4yr-gye83jo8yx.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=gye83jo8yx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g3pw7iimyz", "Integrity": "DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 12244, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\5rxbwjmfei-hrwsygsryq.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2knqut3gi", "Integrity": "YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114877, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\6lcsfls6zf-xvp3kq03qx.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=xvp3kq03qx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z7y6dfz9j3", "Integrity": "vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6975, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\7xm08l4ixn-c2jlpeoesf.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bpa5g0wlhg", "Integrity": "keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 33080, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\9eonhfmzpl-wf6sfai52w.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=wf6sfai52w}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ktgldfzmo5", "Integrity": "aMHA9ZETcWIScTmE/qW8GnAB6Vq3bPkoZzYb2CIqDn8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64236, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\anlqmrsivh-t6ifcaearb.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Computed", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "Miller.WMS.Web#[.{fingerprint=t6ifcaearb}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Miller.WMS.Web.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mwsowy9c7o", "Integrity": "ViCpKwXJbTh6AF3wUPCXq8Pymvoi1RewPislSiGnlWI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Miller.WMS.Web.styles.css", "FileLength": 1729, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\anq611cgxs-rxsg74s51o.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rxsg74s51o}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fjx614p1f2", "Integrity": "tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3395, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\awa0pufkqj-fsbi9cje9m.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ggfb0v5ylw", "Integrity": "ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12568, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\c6bkz3byyv-22vffe00uq.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=22vffe00uq}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ki2uzdiv5", "Integrity": "cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 6108, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\clxqqlagr5-tmc1g35s3z.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=tmc1g35s3z}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8wl3mrbh96", "Integrity": "mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3204, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\cu47vy4gfj-nvvlpmu67g.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0gru265j2n", "Integrity": "cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24456, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\dj9hlm0kzn-keugtjm085.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=keugtjm085}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b271r4kg0j", "Integrity": "A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11132, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\et9hy8paoo-wl58j5mj3v.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=wl58j5mj3v}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2dzz7zk1je", "Integrity": "Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11153, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\fsqqj96pld-d4r6k3f320.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=d4r6k3f320}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a9e8v2fr2b", "Integrity": "0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 12194, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\g3m6bm0i7l-cwuvm2sdc3.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=cwuvm2sdc3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3uebi1j6we", "Integrity": "28V7g48B1eUrV0CJSnJvCle1DISFctbwHTG261ZK06k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 38707, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\g82huob31c-ja11lcg8ur.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=ja11lcg8ur}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i8nhkk7sb8", "Integrity": "HpyKGSNAJmvFW2JQ2rr+8pCNjbm/lCjXKEM1Hm6hFyc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 38013, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\giyfa7et8b-sejl45xvog.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=sejl45xvog}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ru0tedr80l", "Integrity": "k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 6106, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\hfcgtdi7p3-pj5nd1wqec.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glev5uv9kg", "Integrity": "pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 114902, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\j9hje8ybqm-qesaa3a1fm.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=qesaa3a1fm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yhemft0x44", "Integrity": "GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3406, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\kbig5l79ed-k72fsduyas.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=k72fsduyas}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f8y9r0oetv", "Integrity": "ce96TscBDwGaFjkGnwgZLT7xSSHzsiTTGlqMq4K/oSo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64493, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\lebqv9thpd-t6ifcaearb.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Computed", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "Miller.WMS.Web#[.{fingerprint=t6ifcaearb}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Miller.WMS.Web.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mwsowy9c7o", "Integrity": "ViCpKwXJbTh6AF3wUPCXq8Pymvoi1RewPislSiGnlWI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Miller.WMS.Web.bundle.scp.css", "FileLength": 1729, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\lndg2s5im1-aexeepp0ev.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3latwtrz94", "Integrity": "1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 14072, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\mkubolfew3-q9ht133ko3.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=q9ht133ko3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d645a0veuj", "Integrity": "ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3235, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\pywypbh9qv-252a5wndhh.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=252a5wndhh}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ktya8jz5ov", "Integrity": "yHJ5EJwF8Y0lQ8hjR9PSjERAHVR5TWbBlCKMsKA/Txs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 32944, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\q03xumkmu6-fxquxrv84i.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=fxquxrv84i}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xhofi<PERSON><PERSON>", "Integrity": "xUN1UfXISZUIRgFENmQNtzFAJfWc0jpSWPRdfTjARpM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92278, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\q4jj7tbtng-okq9zf051y.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=okq9zf051y}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3zm0g72duv", "Integrity": "T2CWaRF2o4EKi5SNbaW3sd3m7mKag5LAdw/EdDU9S58=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86520, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\qjz6howncd-ze3dr5b7df.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=ze3dr5b7df}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2kg58l0pnq", "Integrity": "Ep8TXPdKe1BRQmMaiwjzGLpqIqCsa8JeziGfy91QOfU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 26285, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\qkk0jggjnv-j5mq2jizvt.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pnmw3mh9ht", "Integrity": "tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44237, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\scf3j3m3nr-v0zj4ognzu.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77t5enldb7", "Integrity": "Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91960, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\sll4euyegn-d3h8l9wove.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "app#[.{fingerprint=d3h8l9wove}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fcjvn85lhy", "Integrity": "UWWnUOvQ8BaUL2Mrm0bFRrZ6+G9ohT6UBCOo1O347Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\app.css", "FileLength": 1511, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\t7jrmhh9j1-jd9uben2k1.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ldvkm706vq", "Integrity": "J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15028, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\tf4cmeivg9-t1cqhe9u97.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=t1cqhe9u97}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4t5zwq0ztq", "Integrity": "KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6973, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\tw7pasprv0-zub09dkrxp.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=zub09dkrxp}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ndo96zskmb", "Integrity": "q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33770, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\ujxopsqs3c-ynyaa8k90p.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=ynyaa8k90p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ws8e397h8g", "Integrity": "EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33667, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\vxrqzkb88z-ee0r1s7dh0.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kqk14ew2nl", "Integrity": "PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25726, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\w4wnu48mx3-fvhpjtyr6v.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5b7ig2cj79", "Integrity": "fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25714, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\x8ftxzgmlw-ausgxo2sd3.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnst782kog", "Integrity": "ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 33061, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\xmyml8ppsk-43atpzeawx.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=43atpzeawx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ny7oqyylde", "Integrity": "sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 31123, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\y1nec1zgnj-c2oey78nd0.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oiolxl3gck", "Integrity": "oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24487, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\ygu81wg92r-c63t5i9ira.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=c63t5i9ira}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a66bztmsz2", "Integrity": "AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 31142, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\z5dgsvywh2-iy2auvubsp.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=iy2auvubsp}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42jbnex9ve", "Integrity": "PPRimh2a7UdzoAH+CvP1whLwkhlzs0R8qUK8MPWML2Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 54528, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zcnnnh36ui-cosvhxvwiu.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f178oapzb7", "Integrity": "XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 14093, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zn1yxmpder-r37jpkscte.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=r37jpkscte}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1vtrgsyhbw", "Integrity": "E9AKsWN43PnjF/JB5K3R3IOVOy282C8xJ7j67Ywpn9Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55527, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zrm5bgsn2i-ft3s53vfgj.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uom9ela1zq", "Integrity": "4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91866, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Miller.WMS.Web.styles.css", "SourceId": "Miller.WMS.Web", "SourceType": "Computed", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "Miller.WMS.Web#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "t6ifcaearb", "Integrity": "K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Miller.WMS.Web.styles.css", "FileLength": 5800, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Miller.WMS.Web.bundle.scp.css", "SourceId": "Miller.WMS.Web", "SourceType": "Computed", "ContentRoot": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "Miller.WMS.Web#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "t6ifcaearb", "Integrity": "K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Miller.WMS.Web.bundle.scp.css", "FileLength": 5800, "LastWriteTime": "2025-08-05T20:55:56+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\app.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "d3h8l9wove", "Integrity": "d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 2825, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\favicon.png", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zub09dkrxp", "Integrity": "CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 293102, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "43atpzeawx", "Integrity": "sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232808, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ynyaa8k90p", "Integrity": "/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 292288, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c63t5i9ira", "Integrity": "rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232916, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "t1cqhe9u97", "Integrity": "f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 74413, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sejl45xvog", "Integrity": "hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51800, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xvp3kq03qx", "Integrity": "M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 74486, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "22vffe00uq", "Integrity": "+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51875, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qesaa3a1fm", "Integrity": "rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12661, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tmc1g35s3z", "Integrity": "y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10131, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rxsg74s51o", "Integrity": "gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12651, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "q9ht133ko3", "Integrity": "UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10203, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gye83jo8yx", "Integrity": "uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 113224, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wl58j5mj3v", "Integrity": "ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85357, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "d4r6k3f320", "Integrity": "MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 113083, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "keugtjm085", "Integrity": "7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85286, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "iy2auvubsp", "Integrity": "hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 231707, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fxquxrv84i", "Integrity": "0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "252a5wndhh", "Integrity": "Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 98301, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "okq9zf051y", "Integrity": "ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ja11lcg8ur", "Integrity": "y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 157798, "LastWriteTime": "2025-08-05T20:54:53+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wf6sfai52w", "Integrity": "ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-08-05T20:54:54+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "n5tfi6zt97", "Integrity": "W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 91531, "LastWriteTime": "2025-08-05T20:54:54+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eve9uzuztn", "Integrity": "ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-08-05T20:54:54+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cwuvm2sdc3", "Integrity": "LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 167417, "LastWriteTime": "2025-08-05T20:54:54+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k72fsduyas", "Integrity": "8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-08-05T20:54:54+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ze3dr5b7df", "Integrity": "dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 78215, "LastWriteTime": "2025-08-05T20:54:54+00:00"}, {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "r37jpkscte", "Integrity": "SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-08-05T20:54:54+00:00"}], "Endpoints": [{"Route": "app.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\sll4euyegn-d3h8l9wove.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000661375661"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1511"}, {"Name": "ETag", "Value": "\"UWWnUOvQ8BaUL2Mrm0bFRrZ6+G9ohT6UBCOo1O347Iw=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8="}]}, {"Route": "app.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2825"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8="}]}, {"Route": "app.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\sll4euyegn-d3h8l9wove.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1511"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UWWnUOvQ8BaUL2Mrm0bFRrZ6+G9ohT6UBCOo1O347Iw=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UWWnUOvQ8BaUL2Mrm0bFRrZ6+G9ohT6UBCOo1O347Iw="}]}, {"Route": "app.d3h8l9wove.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\sll4euyegn-d3h8l9wove.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000661375661"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1511"}, {"Name": "ETag", "Value": "\"UWWnUOvQ8BaUL2Mrm0bFRrZ6+G9ohT6UBCOo1O347Iw=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d3h8l9wove"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8="}]}, {"Route": "app.d3h8l9wove.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2825"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d3h8l9wove"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-d/7ggTEbxDDlTAWr+gOJEIqRLsoQXv+V7yZv/Bm8Ll8="}]}, {"Route": "app.d3h8l9wove.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\sll4euyegn-d3h8l9wove.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1511"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UWWnUOvQ8BaUL2Mrm0bFRrZ6+G9ohT6UBCOo1O347Iw=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d3h8l9wove"}, {"Name": "label", "Value": "app.css.gz"}, {"Name": "integrity", "Value": "sha256-UWWnUOvQ8BaUL2Mrm0bFRrZ6+G9ohT6UBCOo1O347Iw="}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "label", "Value": "favicon.png"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "favicon.png", "AssetFile": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\tw7pasprv0-zub09dkrxp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029611205"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33770"}, {"Name": "ETag", "Value": "\"q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "293102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\tw7pasprv0-zub09dkrxp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33770"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\hfcgtdi7p3-pj5nd1wqec.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008702993"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114902"}, {"Name": "ETag", "Value": "\"pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\hfcgtdi7p3-pj5nd1wqec.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114902"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\hfcgtdi7p3-pj5nd1wqec.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008702993"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114902"}, {"Name": "ETag", "Value": "\"pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}, {"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}, {"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\hfcgtdi7p3-pj5nd1wqec.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114902"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map.gz"}, {"Name": "integrity", "Value": "sha256-pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.43atpzeawx.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\xmyml8ppsk-43atpzeawx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032129546"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "31123"}, {"Name": "ETag", "Value": "\"sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "43atpzeawx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.43atpzeawx.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232808"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "43atpzeawx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.43atpzeawx.css.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\xmyml8ppsk-43atpzeawx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "31123"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "43atpzeawx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.gz"}, {"Name": "integrity", "Value": "sha256-sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\xmyml8ppsk-43atpzeawx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032129546"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "31123"}, {"Name": "ETag", "Value": "\"sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232808"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\xmyml8ppsk-43atpzeawx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "31123"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\scf3j3m3nr-v0zj4ognzu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010874175"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91960"}, {"Name": "ETag", "Value": "\"Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589892"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\scf3j3m3nr-v0zj4ognzu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91960"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\scf3j3m3nr-v0zj4ognzu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010874175"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91960"}, {"Name": "ETag", "Value": "\"Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589892"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\scf3j3m3nr-v0zj4ognzu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91960"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\ujxopsqs3c-ynyaa8k90p.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029701794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33667"}, {"Name": "ETag", "Value": "\"EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "292288"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\ujxopsqs3c-ynyaa8k90p.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33667"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\5rxbwjmfei-hrwsygsryq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008704887"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114877"}, {"Name": "ETag", "Value": "\"YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679615"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\5rxbwjmfei-hrwsygsryq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114877"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz"}, {"Name": "integrity", "Value": "sha256-YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\5rxbwjmfei-hrwsygsryq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008704887"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114877"}, {"Name": "ETag", "Value": "\"YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679615"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\5rxbwjmfei-hrwsygsryq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "114877"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.c63t5i9ira.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\ygu81wg92r-c63t5i9ira.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032109944"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "31142"}, {"Name": "ETag", "Value": "\"AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c63t5i9ira"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.c63t5i9ira.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c63t5i9ira"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.c63t5i9ira.css.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\ygu81wg92r-c63t5i9ira.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "31142"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c63t5i9ira"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz"}, {"Name": "integrity", "Value": "sha256-AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\ygu81wg92r-c63t5i9ira.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032109944"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "31142"}, {"Name": "ETag", "Value": "\"AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zrm5bgsn2i-ft3s53vfgj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010885302"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91866"}, {"Name": "ETag", "Value": "\"4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589087"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zrm5bgsn2i-ft3s53vfgj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91866"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\ygu81wg92r-c63t5i9ira.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "31142"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zrm5bgsn2i-ft3s53vfgj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010885302"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91866"}, {"Name": "ETag", "Value": "\"4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589087"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zrm5bgsn2i-ft3s53vfgj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91866"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.ynyaa8k90p.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\ujxopsqs3c-ynyaa8k90p.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029701794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33667"}, {"Name": "ETag", "Value": "\"EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ynyaa8k90p"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Name": "integrity", "Value": "sha256-/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.ynyaa8k90p.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "292288"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ynyaa8k90p"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Name": "integrity", "Value": "sha256-/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.ynyaa8k90p.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\ujxopsqs3c-ynyaa8k90p.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33667"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ynyaa8k90p"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz"}, {"Name": "integrity", "Value": "sha256-EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.zub09dkrxp.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\tw7pasprv0-zub09dkrxp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000029611205"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33770"}, {"Name": "ETag", "Value": "\"q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zub09dkrxp"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}, {"Name": "integrity", "Value": "sha256-CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.zub09dkrxp.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "293102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zub09dkrxp"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}, {"Name": "integrity", "Value": "sha256-CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.zub09dkrxp.css.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\tw7pasprv0-zub09dkrxp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33770"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zub09dkrxp"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.gz"}, {"Name": "integrity", "Value": "sha256-q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\tf4cmeivg9-t1cqhe9u97.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000143389733"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6973"}, {"Name": "ETag", "Value": "\"KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74413"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\7xm08l4ixn-c2jlpeoesf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030228832"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33080"}, {"Name": "ETag", "Value": "\"keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203221"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\7xm08l4ixn-c2jlpeoesf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33080"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz"}, {"Name": "integrity", "Value": "sha256-keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\tf4cmeivg9-t1cqhe9u97.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6973"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\7xm08l4ixn-c2jlpeoesf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030228832"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33080"}, {"Name": "ETag", "Value": "\"keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203221"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\7xm08l4ixn-c2jlpeoesf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33080"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\giyfa7et8b-sejl45xvog.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000163746520"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6106"}, {"Name": "ETag", "Value": "\"k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51800"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\lndg2s5im1-aexeepp0ev.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071058054"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14072"}, {"Name": "ETag", "Value": "\"1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115986"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\lndg2s5im1-aexeepp0ev.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14072"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\giyfa7et8b-sejl45xvog.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6106"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\lndg2s5im1-aexeepp0ev.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071058054"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14072"}, {"Name": "ETag", "Value": "\"1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115986"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\lndg2s5im1-aexeepp0ev.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14072"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.sejl45xvog.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\giyfa7et8b-sejl45xvog.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000163746520"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6106"}, {"Name": "ETag", "Value": "\"k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sejl45xvog"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Name": "integrity", "Value": "sha256-hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.sejl45xvog.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51800"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sejl45xvog"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Name": "integrity", "Value": "sha256-hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.sejl45xvog.css.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\giyfa7et8b-sejl45xvog.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6106"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sejl45xvog"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz"}, {"Name": "integrity", "Value": "sha256-k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\6lcsfls6zf-xvp3kq03qx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000143348624"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6975"}, {"Name": "ETag", "Value": "\"vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74486"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\x8ftxzgmlw-ausgxo2sd3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030246204"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33061"}, {"Name": "ETag", "Value": "\"ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203225"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\x8ftxzgmlw-ausgxo2sd3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33061"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz"}, {"Name": "integrity", "Value": "sha256-ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\6lcsfls6zf-xvp3kq03qx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6975"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\x8ftxzgmlw-ausgxo2sd3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030246204"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33061"}, {"Name": "ETag", "Value": "\"ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203225"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\x8ftxzgmlw-ausgxo2sd3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33061"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.22vffe00uq.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\c6bkz3byyv-22vffe00uq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000163692912"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6108"}, {"Name": "ETag", "Value": "\"cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "22vffe00uq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.22vffe00uq.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51875"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "22vffe00uq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.22vffe00uq.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\c6bkz3byyv-22vffe00uq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6108"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "22vffe00uq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz"}, {"Name": "integrity", "Value": "sha256-cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\c6bkz3byyv-22vffe00uq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000163692912"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6108"}, {"Name": "ETag", "Value": "\"cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51875"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zcnnnh36ui-cosvhxvwiu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000070952178"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14093"}, {"Name": "ETag", "Value": "\"XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "116063"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zcnnnh36ui-cosvhxvwiu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14093"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\c6bkz3byyv-22vffe00uq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6108"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zcnnnh36ui-cosvhxvwiu.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000070952178"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14093"}, {"Name": "ETag", "Value": "\"XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "116063"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zcnnnh36ui-cosvhxvwiu.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14093"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.xvp3kq03qx.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\6lcsfls6zf-xvp3kq03qx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000143348624"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6975"}, {"Name": "ETag", "Value": "\"vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xvp3kq03qx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Name": "integrity", "Value": "sha256-M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.xvp3kq03qx.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74486"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xvp3kq03qx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Name": "integrity", "Value": "sha256-M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.xvp3kq03qx.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\6lcsfls6zf-xvp3kq03qx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6975"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xvp3kq03qx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz"}, {"Name": "integrity", "Value": "sha256-vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.t1cqhe9u97.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\tf4cmeivg9-t1cqhe9u97.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000143389733"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6973"}, {"Name": "ETag", "Value": "\"KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t1cqhe9u97"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Name": "integrity", "Value": "sha256-f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.t1cqhe9u97.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74413"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t1cqhe9u97"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Name": "integrity", "Value": "sha256-f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.t1cqhe9u97.css.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\tf4cmeivg9-t1cqhe9u97.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6973"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t1cqhe9u97"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.gz"}, {"Name": "integrity", "Value": "sha256-KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\j9hje8ybqm-qesaa3a1fm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000293513355"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3406"}, {"Name": "ETag", "Value": "\"GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12661"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\w4wnu48mx3-fvhpjtyr6v.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038887809"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25714"}, {"Name": "ETag", "Value": "\"fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129371"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\w4wnu48mx3-fvhpjtyr6v.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25714"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz"}, {"Name": "integrity", "Value": "sha256-fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\j9hje8ybqm-qesaa3a1fm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3406"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\w4wnu48mx3-fvhpjtyr6v.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038887809"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25714"}, {"Name": "ETag", "Value": "\"fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129371"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\w4wnu48mx3-fvhpjtyr6v.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25714"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\clxqqlagr5-tmc1g35s3z.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000312012480"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3204"}, {"Name": "ETag", "Value": "\"mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\awa0pufkqj-fsbi9cje9m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079560824"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12568"}, {"Name": "ETag", "Value": "\"ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51369"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\awa0pufkqj-fsbi9cje9m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12568"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\clxqqlagr5-tmc1g35s3z.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3204"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\awa0pufkqj-fsbi9cje9m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079560824"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12568"}, {"Name": "ETag", "Value": "\"ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51369"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\awa0pufkqj-fsbi9cje9m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12568"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.tmc1g35s3z.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\clxqqlagr5-tmc1g35s3z.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000312012480"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3204"}, {"Name": "ETag", "Value": "\"mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tmc1g35s3z"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Name": "integrity", "Value": "sha256-y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.tmc1g35s3z.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10131"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tmc1g35s3z"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Name": "integrity", "Value": "sha256-y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.tmc1g35s3z.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\clxqqlagr5-tmc1g35s3z.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3204"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tmc1g35s3z"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz"}, {"Name": "integrity", "Value": "sha256-mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.qesaa3a1fm.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\j9hje8ybqm-qesaa3a1fm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000293513355"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3406"}, {"Name": "ETag", "Value": "\"GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qesaa3a1fm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Name": "integrity", "Value": "sha256-rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.qesaa3a1fm.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12661"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qesaa3a1fm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Name": "integrity", "Value": "sha256-rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.qesaa3a1fm.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\j9hje8ybqm-qesaa3a1fm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3406"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qesaa3a1fm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz"}, {"Name": "integrity", "Value": "sha256-GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\anq611cgxs-rxsg74s51o.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000294464075"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3395"}, {"Name": "ETag", "Value": "\"tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12651"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\vxrqzkb88z-ee0r1s7dh0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038869670"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25726"}, {"Name": "ETag", "Value": "\"PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129386"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\vxrqzkb88z-ee0r1s7dh0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25726"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz"}, {"Name": "integrity", "Value": "sha256-PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\anq611cgxs-rxsg74s51o.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\vxrqzkb88z-ee0r1s7dh0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038869670"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25726"}, {"Name": "ETag", "Value": "\"PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129386"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\vxrqzkb88z-ee0r1s7dh0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25726"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\mkubolfew3-q9ht133ko3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000309023486"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3235"}, {"Name": "ETag", "Value": "\"ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10203"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\mkubolfew3-q9ht133ko3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3235"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\t7jrmhh9j1-jd9uben2k1.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066538026"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15028"}, {"Name": "ETag", "Value": "\"J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63943"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\t7jrmhh9j1-jd9uben2k1.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15028"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\t7jrmhh9j1-jd9uben2k1.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066538026"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15028"}, {"Name": "ETag", "Value": "\"J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63943"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\t7jrmhh9j1-jd9uben2k1.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15028"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.q9ht133ko3.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\mkubolfew3-q9ht133ko3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000309023486"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3235"}, {"Name": "ETag", "Value": "\"ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9ht133ko3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.q9ht133ko3.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10203"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9ht133ko3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.q9ht133ko3.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\mkubolfew3-q9ht133ko3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3235"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9ht133ko3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz"}, {"Name": "integrity", "Value": "sha256-ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rxsg74s51o.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\anq611cgxs-rxsg74s51o.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000294464075"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3395"}, {"Name": "ETag", "Value": "\"tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rxsg74s51o"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Name": "integrity", "Value": "sha256-gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rxsg74s51o.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12651"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rxsg74s51o"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Name": "integrity", "Value": "sha256-gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rxsg74s51o.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\anq611cgxs-rxsg74s51o.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rxsg74s51o"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz"}, {"Name": "integrity", "Value": "sha256-tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\3wy4uav4yr-gye83jo8yx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000081665986"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12244"}, {"Name": "ETag", "Value": "\"DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "113224"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\3wy4uav4yr-gye83jo8yx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12244"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\1y5rswsd3k-r4e9w2rdcm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022590191"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44266"}, {"Name": "ETag", "Value": "\"JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\1y5rswsd3k-r4e9w2rdcm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44266"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\1y5rswsd3k-r4e9w2rdcm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022590191"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44266"}, {"Name": "ETag", "Value": "\"JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\1y5rswsd3k-r4e9w2rdcm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44266"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz"}, {"Name": "integrity", "Value": "sha256-JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.gye83jo8yx.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\3wy4uav4yr-gye83jo8yx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000081665986"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12244"}, {"Name": "ETag", "Value": "\"DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gye83jo8yx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Name": "integrity", "Value": "sha256-uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.gye83jo8yx.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "113224"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gye83jo8yx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Name": "integrity", "Value": "sha256-uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.gye83jo8yx.css.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\3wy4uav4yr-gye83jo8yx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12244"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gye83jo8yx"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz"}, {"Name": "integrity", "Value": "sha256-DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\et9hy8paoo-wl58j5mj3v.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000089653936"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11153"}, {"Name": "ETag", "Value": "\"Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85357"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\y1nec1zgnj-c2oey78nd0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000040836328"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24487"}, {"Name": "ETag", "Value": "\"oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\y1nec1zgnj-c2oey78nd0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24487"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\et9hy8paoo-wl58j5mj3v.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11153"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\y1nec1zgnj-c2oey78nd0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000040836328"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24487"}, {"Name": "ETag", "Value": "\"oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\y1nec1zgnj-c2oey78nd0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24487"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.wl58j5mj3v.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\et9hy8paoo-wl58j5mj3v.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000089653936"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11153"}, {"Name": "ETag", "Value": "\"Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl58j5mj3v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Name": "integrity", "Value": "sha256-ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.wl58j5mj3v.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85357"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl58j5mj3v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Name": "integrity", "Value": "sha256-ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.wl58j5mj3v.css.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\et9hy8paoo-wl58j5mj3v.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11153"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl58j5mj3v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz"}, {"Name": "integrity", "Value": "sha256-Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\fsqqj96pld-d4r6k3f320.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000082000820"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12194"}, {"Name": "ETag", "Value": "\"0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "113083"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\fsqqj96pld-d4r6k3f320.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12194"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\qkk0jggjnv-j5mq2jizvt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022605000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44237"}, {"Name": "ETag", "Value": "\"tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267476"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\qkk0jggjnv-j5mq2jizvt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44237"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz"}, {"Name": "integrity", "Value": "sha256-tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\qkk0jggjnv-j5mq2jizvt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022605000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44237"}, {"Name": "ETag", "Value": "\"tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267476"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\qkk0jggjnv-j5mq2jizvt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44237"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.d4r6k3f320.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\fsqqj96pld-d4r6k3f320.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000082000820"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12194"}, {"Name": "ETag", "Value": "\"0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d4r6k3f320"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Name": "integrity", "Value": "sha256-MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.d4r6k3f320.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "113083"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d4r6k3f320"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Name": "integrity", "Value": "sha256-MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.d4r6k3f320.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\fsqqj96pld-d4r6k3f320.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12194"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d4r6k3f320"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz"}, {"Name": "integrity", "Value": "sha256-0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\dj9hlm0kzn-keugtjm085.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000089823049"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11132"}, {"Name": "ETag", "Value": "\"A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85286"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\dj9hlm0kzn-keugtjm085.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11132"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\cu47vy4gfj-nvvlpmu67g.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000040888089"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24456"}, {"Name": "ETag", "Value": "\"cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\cu47vy4gfj-nvvlpmu67g.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24456"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\cu47vy4gfj-nvvlpmu67g.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000040888089"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24456"}, {"Name": "ETag", "Value": "\"cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\cu47vy4gfj-nvvlpmu67g.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24456"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.keugtjm085.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\dj9hlm0kzn-keugtjm085.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000089823049"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11132"}, {"Name": "ETag", "Value": "\"A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "keugtjm085"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.keugtjm085.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85286"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "keugtjm085"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.keugtjm085.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\dj9hlm0kzn-keugtjm085.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11132"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "keugtjm085"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz"}, {"Name": "integrity", "Value": "sha256-A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.iy2auvubsp.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\z5dgsvywh2-iy2auvubsp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018338866"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54528"}, {"Name": "ETag", "Value": "\"PPRimh2a7UdzoAH+CvP1whLwkhlzs0R8qUK8MPWML2Y=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iy2auvubsp"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Name": "integrity", "Value": "sha256-hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.iy2auvubsp.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "231707"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iy2auvubsp"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Name": "integrity", "Value": "sha256-hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.iy2auvubsp.js.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\z5dgsvywh2-iy2auvubsp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54528"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PPRimh2a7UdzoAH+CvP1whLwkhlzs0R8qUK8MPWML2Y=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iy2auvubsp"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz"}, {"Name": "integrity", "Value": "sha256-PPRimh2a7UdzoAH+CvP1whLwkhlzs0R8qUK8MPWML2Y="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\z5dgsvywh2-iy2auvubsp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018338866"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54528"}, {"Name": "ETag", "Value": "\"PPRimh2a7UdzoAH+CvP1whLwkhlzs0R8qUK8MPWML2Y=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "231707"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hnh9a7QrpRnUQYojwnFxkhLSYUEuuKB5XFcQzheHqs4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.fxquxrv84i.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\q03xumkmu6-fxquxrv84i.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010836702"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "92278"}, {"Name": "ETag", "Value": "\"xUN1UfXISZUIRgFENmQNtzFAJfWc0jpSWPRdfTjARpM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fxquxrv84i"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Name": "integrity", "Value": "sha256-0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.fxquxrv84i.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "444579"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fxquxrv84i"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Name": "integrity", "Value": "sha256-0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.fxquxrv84i.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\q03xumkmu6-fxquxrv84i.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "92278"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xUN1UfXISZUIRgFENmQNtzFAJfWc0jpSWPRdfTjARpM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fxquxrv84i"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz"}, {"Name": "integrity", "Value": "sha256-xUN1UfXISZUIRgFENmQNtzFAJfWc0jpSWPRdfTjARpM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\z5dgsvywh2-iy2auvubsp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54528"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PPRimh2a7UdzoAH+CvP1whLwkhlzs0R8qUK8MPWML2Y=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PPRimh2a7UdzoAH+CvP1whLwkhlzs0R8qUK8MPWML2Y="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\q03xumkmu6-fxquxrv84i.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010836702"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "92278"}, {"Name": "ETag", "Value": "\"xUN1UfXISZUIRgFENmQNtzFAJfWc0jpSWPRdfTjARpM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "444579"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0+ATa14wT8hrhGSdf4/yBYkwzpGRsd2oaFTmCbZ1V+Y="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\q03xumkmu6-fxquxrv84i.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "92278"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xUN1UfXISZUIRgFENmQNtzFAJfWc0jpSWPRdfTjARpM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xUN1UfXISZUIRgFENmQNtzFAJfWc0jpSWPRdfTjARpM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.252a5wndhh.js", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\pywypbh9qv-252a5wndhh.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030353620"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32944"}, {"Name": "ETag", "Value": "\"yHJ5EJwF8Y0lQ8hjR9PSjERAHVR5TWbBlCKMsKA/Txs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "252a5wndhh"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.252a5wndhh.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "98301"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "252a5wndhh"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.252a5wndhh.js.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\pywypbh9qv-252a5wndhh.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32944"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yHJ5EJwF8Y0lQ8hjR9PSjERAHVR5TWbBlCKMsKA/Txs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "252a5wndhh"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz"}, {"Name": "integrity", "Value": "sha256-yHJ5EJwF8Y0lQ8hjR9PSjERAHVR5TWbBlCKMsKA/Txs="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\pywypbh9qv-252a5wndhh.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030353620"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32944"}, {"Name": "ETag", "Value": "\"yHJ5EJwF8Y0lQ8hjR9PSjERAHVR5TWbBlCKMsKA/Txs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "98301"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vn4ElCO4/V1IVm1F8cDhBSb0i34rLiFc5U9iMdVUi/A="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\pywypbh9qv-252a5wndhh.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32944"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yHJ5EJwF8Y0lQ8hjR9PSjERAHVR5TWbBlCKMsKA/Txs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yHJ5EJwF8Y0lQ8hjR9PSjERAHVR5TWbBlCKMsKA/Txs="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\q4jj7tbtng-okq9zf051y.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011557888"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86520"}, {"Name": "ETag", "Value": "\"T2CWaRF2o4EKi5SNbaW3sd3m7mKag5LAdw/EdDU9S58=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "332090"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\q4jj7tbtng-okq9zf051y.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86520"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"T2CWaRF2o4EKi5SNbaW3sd3m7mKag5LAdw/EdDU9S58=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T2CWaRF2o4EKi5SNbaW3sd3m7mKag5LAdw/EdDU9S58="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.okq9zf051y.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\q4jj7tbtng-okq9zf051y.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011557888"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86520"}, {"Name": "ETag", "Value": "\"T2CWaRF2o4EKi5SNbaW3sd3m7mKag5LAdw/EdDU9S58=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "okq9zf051y"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Name": "integrity", "Value": "sha256-ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.okq9zf051y.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "332090"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "okq9zf051y"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Name": "integrity", "Value": "sha256-ODJ4Y7doNYL90rfuQDw+Cpq4wiKEjDo35jIkrukzQFw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.okq9zf051y.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\q4jj7tbtng-okq9zf051y.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86520"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"T2CWaRF2o4EKi5SNbaW3sd3m7mKag5LAdw/EdDU9S58=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "okq9zf051y"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz"}, {"Name": "integrity", "Value": "sha256-T2CWaRF2o4EKi5SNbaW3sd3m7mKag5LAdw/EdDU9S58="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.cwuvm2sdc3.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\g3m6bm0i7l-cwuvm2sdc3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000025834453"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38707"}, {"Name": "ETag", "Value": "\"28V7g48B1eUrV0CJSnJvCle1DISFctbwHTG261ZK06k=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cwuvm2sdc3"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}, {"Name": "integrity", "Value": "sha256-LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.cwuvm2sdc3.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "167417"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cwuvm2sdc3"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}, {"Name": "integrity", "Value": "sha256-LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.cwuvm2sdc3.js.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\g3m6bm0i7l-cwuvm2sdc3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38707"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"28V7g48B1eUrV0CJSnJvCle1DISFctbwHTG261ZK06k=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cwuvm2sdc3"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.gz"}, {"Name": "integrity", "Value": "sha256-28V7g48B1eUrV0CJSnJvCle1DISFctbwHTG261ZK06k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.ja11lcg8ur.js", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\g82huob31c-ja11lcg8ur.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000026306098"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38013"}, {"Name": "ETag", "Value": "\"HpyKGSNAJmvFW2JQ2rr+8pCNjbm/lCjXKEM1Hm6hFyc=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ja11lcg8ur"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Name": "integrity", "Value": "sha256-y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.ja11lcg8ur.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "157798"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ja11lcg8ur"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Name": "integrity", "Value": "sha256-y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.ja11lcg8ur.js.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\g82huob31c-ja11lcg8ur.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38013"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HpyKGSNAJmvFW2JQ2rr+8pCNjbm/lCjXKEM1Hm6hFyc=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ja11lcg8ur"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.gz"}, {"Name": "integrity", "Value": "sha256-HpyKGSNAJmvFW2JQ2rr+8pCNjbm/lCjXKEM1Hm6hFyc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\g82huob31c-ja11lcg8ur.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000026306098"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38013"}, {"Name": "ETag", "Value": "\"HpyKGSNAJmvFW2JQ2rr+8pCNjbm/lCjXKEM1Hm6hFyc=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "157798"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y6GHttQbrYE2eIxqrCeAloC/daRuAxJsF3R+sWUXoro="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\g82huob31c-ja11lcg8ur.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38013"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HpyKGSNAJmvFW2JQ2rr+8pCNjbm/lCjXKEM1Hm6hFyc=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HpyKGSNAJmvFW2JQ2rr+8pCNjbm/lCjXKEM1Hm6hFyc="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\9eonhfmzpl-wf6sfai52w.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015567352"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64236"}, {"Name": "ETag", "Value": "\"aMHA9ZETcWIScTmE/qW8GnAB6Vq3bPkoZzYb2CIqDn8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "305438"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\9eonhfmzpl-wf6sfai52w.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64236"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"aMHA9ZETcWIScTmE/qW8GnAB6Vq3bPkoZzYb2CIqDn8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aMHA9ZETcWIScTmE/qW8GnAB6Vq3bPkoZzYb2CIqDn8="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.wf6sfai52w.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\9eonhfmzpl-wf6sfai52w.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015567352"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64236"}, {"Name": "ETag", "Value": "\"aMHA9ZETcWIScTmE/qW8GnAB6Vq3bPkoZzYb2CIqDn8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wf6sfai52w"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Name": "integrity", "Value": "sha256-ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.wf6sfai52w.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "305438"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wf6sfai52w"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Name": "integrity", "Value": "sha256-ZLlYqmpd0+HYXoPrbbyWE17RYi/epKqKnwyHR8AUFEY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.wf6sfai52w.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\9eonhfmzpl-wf6sfai52w.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64236"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"aMHA9ZETcWIScTmE/qW8GnAB6Vq3bPkoZzYb2CIqDn8=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wf6sfai52w"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz"}, {"Name": "integrity", "Value": "sha256-aMHA9ZETcWIScTmE/qW8GnAB6Vq3bPkoZzYb2CIqDn8="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\1bpdowd3ur-n5tfi6zt97.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000035459735"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28200"}, {"Name": "ETag", "Value": "\"Hd51/ICuZDXnORaSusIoHCgUhhYiqlw+xpRZYL70fuY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91531"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.eve9uzuztn.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\2aijt2o6r4-eve9uzuztn.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017734270"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56387"}, {"Name": "ETag", "Value": "\"EJxn9g8lywGEK14NsSRZd2K3+UXEihxQuQQ9RrZuoJM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eve9uzuztn"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Name": "integrity", "Value": "sha256-ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.eve9uzuztn.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "222455"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eve9uzuztn"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Name": "integrity", "Value": "sha256-ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.eve9uzuztn.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\2aijt2o6r4-eve9uzuztn.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56387"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EJxn9g8lywGEK14NsSRZd2K3+UXEihxQuQQ9RrZuoJM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eve9uzuztn"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz"}, {"Name": "integrity", "Value": "sha256-EJxn9g8lywGEK14NsSRZd2K3+UXEihxQuQQ9RrZuoJM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\1bpdowd3ur-n5tfi6zt97.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28200"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Hd51/ICuZDXnORaSusIoHCgUhhYiqlw+xpRZYL70fuY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Hd51/ICuZDXnORaSusIoHCgUhhYiqlw+xpRZYL70fuY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\2aijt2o6r4-eve9uzuztn.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017734270"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56387"}, {"Name": "ETag", "Value": "\"EJxn9g8lywGEK14NsSRZd2K3+UXEihxQuQQ9RrZuoJM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "222455"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ip2rfa0MvXZE/bzq7CtR/Goc6noU4Excom7ubU3N7Gw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\2aijt2o6r4-eve9uzuztn.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56387"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EJxn9g8lywGEK14NsSRZd2K3+UXEihxQuQQ9RrZuoJM=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EJxn9g8lywGEK14NsSRZd2K3+UXEihxQuQQ9RrZuoJM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.n5tfi6zt97.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\1bpdowd3ur-n5tfi6zt97.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000035459735"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28200"}, {"Name": "ETag", "Value": "\"Hd51/ICuZDXnORaSusIoHCgUhhYiqlw+xpRZYL70fuY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n5tfi6zt97"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Name": "integrity", "Value": "sha256-W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.n5tfi6zt97.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91531"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n5tfi6zt97"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Name": "integrity", "Value": "sha256-W9tJug6/1iJjGFpphOndO3XCkewFNWbAbKvffva6RZs="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.n5tfi6zt97.js.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\1bpdowd3ur-n5tfi6zt97.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28200"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Hd51/ICuZDXnORaSusIoHCgUhhYiqlw+xpRZYL70fuY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n5tfi6zt97"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz"}, {"Name": "integrity", "Value": "sha256-Hd51/ICuZDXnORaSusIoHCgUhhYiqlw+xpRZYL70fuY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\g3m6bm0i7l-cwuvm2sdc3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000025834453"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38707"}, {"Name": "ETag", "Value": "\"28V7g48B1eUrV0CJSnJvCle1DISFctbwHTG261ZK06k=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "167417"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LMxmaBm9/agbSVNqYkg222AZLkvsWyWurGzC28sOE0I="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\g3m6bm0i7l-cwuvm2sdc3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38707"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"28V7g48B1eUrV0CJSnJvCle1DISFctbwHTG261ZK06k=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-28V7g48B1eUrV0CJSnJvCle1DISFctbwHTG261ZK06k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.k72fsduyas.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\kbig5l79ed-k72fsduyas.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015505318"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64493"}, {"Name": "ETag", "Value": "\"ce96TscBDwGaFjkGnwgZLT7xSSHzsiTTGlqMq4K/oSo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k72fsduyas"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}, {"Name": "integrity", "Value": "sha256-8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.k72fsduyas.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "306606"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k72fsduyas"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}, {"Name": "integrity", "Value": "sha256-8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.k72fsduyas.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\kbig5l79ed-k72fsduyas.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64493"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ce96TscBDwGaFjkGnwgZLT7xSSHzsiTTGlqMq4K/oSo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k72fsduyas"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map.gz"}, {"Name": "integrity", "Value": "sha256-ce96TscBDwGaFjkGnwgZLT7xSSHzsiTTGlqMq4K/oSo="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\kbig5l79ed-k72fsduyas.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015505318"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64493"}, {"Name": "ETag", "Value": "\"ce96TscBDwGaFjkGnwgZLT7xSSHzsiTTGlqMq4K/oSo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "306606"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8AiFSK7kL6c9jCw5cDsWQY9co1IUibWKeP/qfYjq7kk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\kbig5l79ed-k72fsduyas.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64493"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ce96TscBDwGaFjkGnwgZLT7xSSHzsiTTGlqMq4K/oSo=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ce96TscBDwGaFjkGnwgZLT7xSSHzsiTTGlqMq4K/oSo="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\qjz6howncd-ze3dr5b7df.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038043065"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26285"}, {"Name": "ETag", "Value": "\"Ep8TXPdKe1BRQmMaiwjzGLpqIqCsa8JeziGfy91QOfU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "78215"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\qjz6howncd-ze3dr5b7df.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26285"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ep8TXPdKe1BRQmMaiwjzGLpqIqCsa8JeziGfy91QOfU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ep8TXPdKe1BRQmMaiwjzGLpqIqCsa8JeziGfy91QOfU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zn1yxmpder-r37jpkscte.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018008932"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55527"}, {"Name": "ETag", "Value": "\"E9AKsWN43PnjF/JB5K3R3IOVOy282C8xJ7j67Ywpn9Q=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "220561"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zn1yxmpder-r37jpkscte.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55527"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"E9AKsWN43PnjF/JB5K3R3IOVOy282C8xJ7j67Ywpn9Q=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E9AKsWN43PnjF/JB5K3R3IOVOy282C8xJ7j67Ywpn9Q="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.r37jpkscte.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zn1yxmpder-r37jpkscte.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018008932"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55527"}, {"Name": "ETag", "Value": "\"E9AKsWN43PnjF/JB5K3R3IOVOy282C8xJ7j67Ywpn9Q=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r37jpkscte"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Name": "integrity", "Value": "sha256-SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.r37jpkscte.map", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "220561"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r37jpkscte"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Name": "integrity", "Value": "sha256-SQzHIgs8eliwkpuc0B4Mv+Dx0K8QtbXOQiLLWw7UaVY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.r37jpkscte.map.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zn1yxmpder-r37jpkscte.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55527"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"E9AKsWN43PnjF/JB5K3R3IOVOy282C8xJ7j67Ywpn9Q=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r37jpkscte"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz"}, {"Name": "integrity", "Value": "sha256-E9AKsWN43PnjF/JB5K3R3IOVOy282C8xJ7j67Ywpn9Q="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.ze3dr5b7df.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\qjz6howncd-ze3dr5b7df.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038043065"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26285"}, {"Name": "ETag", "Value": "\"Ep8TXPdKe1BRQmMaiwjzGLpqIqCsa8JeziGfy91QOfU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ze3dr5b7df"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}, {"Name": "integrity", "Value": "sha256-dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.ze3dr5b7df.js", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "78215"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:54:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ze3dr5b7df"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}, {"Name": "integrity", "Value": "sha256-dQ4LEVNBHAP5TQISPLiu2/59oUU8sxFUTjEdryh+Gog="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.ze3dr5b7df.js.gz", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\qjz6howncd-ze3dr5b7df.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26285"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ep8TXPdKe1BRQmMaiwjzGLpqIqCsa8JeziGfy91QOfU=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ze3dr5b7df"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.gz"}, {"Name": "integrity", "Value": "sha256-Ep8TXPdKe1BRQmMaiwjzGLpqIqCsa8JeziGfy91QOfU="}]}, {"Route": "Miller.WMS.Web.bundle.scp.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\lebqv9thpd-t6ifcaearb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000578034682"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1729"}, {"Name": "ETag", "Value": "\"ViCpKwXJbTh6AF3wUPCXq8Pymvoi1RewPislSiGnlWI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk="}]}, {"Route": "Miller.WMS.Web.bundle.scp.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Miller.WMS.Web.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5800"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk="}]}, {"Route": "Miller.WMS.Web.bundle.scp.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\lebqv9thpd-t6ifcaearb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1729"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ViCpKwXJbTh6AF3wUPCXq8Pymvoi1RewPislSiGnlWI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ViCpKwXJbTh6AF3wUPCXq8Pymvoi1RewPislSiGnlWI="}]}, {"Route": "Miller.WMS.Web.styles.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\anlqmrsivh-t6ifcaearb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000578034682"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1729"}, {"Name": "ETag", "Value": "\"ViCpKwXJbTh6AF3wUPCXq8Pymvoi1RewPislSiGnlWI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk="}]}, {"Route": "Miller.WMS.Web.styles.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Miller.WMS.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5800"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk="}]}, {"Route": "Miller.WMS.Web.styles.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\anlqmrsivh-t6ifcaearb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1729"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ViCpKwXJbTh6AF3wUPCXq8Pymvoi1RewPislSiGnlWI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ViCpKwXJbTh6AF3wUPCXq8Pymvoi1RewPislSiGnlWI="}]}, {"Route": "Miller.WMS.Web.t6ifcaearb.bundle.scp.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\lebqv9thpd-t6ifcaearb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000578034682"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1729"}, {"Name": "ETag", "Value": "\"ViCpKwXJbTh6AF3wUPCXq8Pymvoi1RewPislSiGnlWI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t6ifcaearb"}, {"Name": "label", "Value": "Miller.WMS.Web.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk="}]}, {"Route": "Miller.WMS.Web.t6ifcaearb.bundle.scp.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Miller.WMS.Web.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5800"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t6ifcaearb"}, {"Name": "label", "Value": "Miller.WMS.Web.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk="}]}, {"Route": "Miller.WMS.Web.t6ifcaearb.bundle.scp.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\lebqv9thpd-t6ifcaearb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1729"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ViCpKwXJbTh6AF3wUPCXq8Pymvoi1RewPislSiGnlWI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t6ifcaearb"}, {"Name": "label", "Value": "Miller.WMS.Web.bundle.scp.css.gz"}, {"Name": "integrity", "Value": "sha256-ViCpKwXJbTh6AF3wUPCXq8Pymvoi1RewPislSiGnlWI="}]}, {"Route": "Miller.WMS.Web.t6ifcaearb.styles.css", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\anlqmrsivh-t6ifcaearb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000578034682"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1729"}, {"Name": "ETag", "Value": "\"ViCpKwXJbTh6AF3wUPCXq8Pymvoi1RewPislSiGnlWI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t6ifcaearb"}, {"Name": "label", "Value": "Miller.WMS.Web.styles.css"}, {"Name": "integrity", "Value": "sha256-K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk="}]}, {"Route": "Miller.WMS.Web.t6ifcaearb.styles.css", "AssetFile": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Miller.WMS.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5800"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t6ifcaearb"}, {"Name": "label", "Value": "Miller.WMS.Web.styles.css"}, {"Name": "integrity", "Value": "sha256-K6JuKBhox1yS+uF7Kp4loFT1mt/54r3DVDZS+ZA04Kk="}]}, {"Route": "Miller.WMS.Web.t6ifcaearb.styles.css.gz", "AssetFile": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\anlqmrsivh-t6ifcaearb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1729"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ViCpKwXJbTh6AF3wUPCXq8Pymvoi1RewPislSiGnlWI=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Aug 2025 20:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t6ifcaearb"}, {"Name": "label", "Value": "Miller.WMS.Web.styles.css.gz"}, {"Name": "integrity", "Value": "sha256-ViCpKwXJbTh6AF3wUPCXq8Pymvoi1RewPislSiGnlWI="}]}]}