Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36203.30 d17.14
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Miller.WMS.AppHost", "Miller.WMS.AppHost\Miller.WMS.AppHost.csproj", "{9E5271F8-A733-45C9-B134-584133A30969}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Miller.WMS.ServiceDefaults", "Miller.WMS.ServiceDefaults\Miller.WMS.ServiceDefaults.csproj", "{C393FAD7-28E0-0789-CE0F-705085E01431}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Miller.WMS.ApiService", "Miller.WMS.ApiService\Miller.WMS.ApiService.csproj", "{FA035236-ED7E-CEAE-E7DA-ABC865C38663}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Miller.WMS.Web", "Miller.WMS.Web\Miller.WMS.Web.csproj", "{AE4E386F-1F79-EEEC-33BF-B2017778B332}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Miller.WMS.Tests", "Miller.WMS.Tests\Miller.WMS.Tests.csproj", "{20B93718-07FD-1108-FCAD-13CE8456DD16}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9E5271F8-A733-45C9-B134-584133A30969}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9E5271F8-A733-45C9-B134-584133A30969}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9E5271F8-A733-45C9-B134-584133A30969}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9E5271F8-A733-45C9-B134-584133A30969}.Release|Any CPU.Build.0 = Release|Any CPU
		{C393FAD7-28E0-0789-CE0F-705085E01431}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C393FAD7-28E0-0789-CE0F-705085E01431}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C393FAD7-28E0-0789-CE0F-705085E01431}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C393FAD7-28E0-0789-CE0F-705085E01431}.Release|Any CPU.Build.0 = Release|Any CPU
		{FA035236-ED7E-CEAE-E7DA-ABC865C38663}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FA035236-ED7E-CEAE-E7DA-ABC865C38663}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FA035236-ED7E-CEAE-E7DA-ABC865C38663}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FA035236-ED7E-CEAE-E7DA-ABC865C38663}.Release|Any CPU.Build.0 = Release|Any CPU
		{AE4E386F-1F79-EEEC-33BF-B2017778B332}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AE4E386F-1F79-EEEC-33BF-B2017778B332}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AE4E386F-1F79-EEEC-33BF-B2017778B332}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AE4E386F-1F79-EEEC-33BF-B2017778B332}.Release|Any CPU.Build.0 = Release|Any CPU
		{20B93718-07FD-1108-FCAD-13CE8456DD16}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{20B93718-07FD-1108-FCAD-13CE8456DD16}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{20B93718-07FD-1108-FCAD-13CE8456DD16}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{20B93718-07FD-1108-FCAD-13CE8456DD16}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {05840D3F-4D13-4EDF-9027-D6ACE772CE86}
	EndGlobalSection
EndGlobal
