{"Version": 1, "WorkspaceRootPath": "C:\\_\\<PERSON>_Github\\Miller.WMS\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{20B93718-07FD-1108-FCAD-13CE8456DD16}|Miller.WMS.Tests\\Miller.WMS.Tests.csproj|c:\\_\\miller_github\\miller.wms\\miller.wms.tests\\webtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{20B93718-07FD-1108-FCAD-13CE8456DD16}|Miller.WMS.Tests\\Miller.WMS.Tests.csproj|solutionrelative:miller.wms.tests\\webtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "WebTests.cs", "DocumentMoniker": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Tests\\WebTests.cs", "RelativeDocumentMoniker": "Miller.WMS.Tests\\WebTests.cs", "ToolTip": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Tests\\WebTests.cs", "RelativeToolTip": "Miller.WMS.Tests\\WebTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAABMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T21:00:05.542Z", "EditorCaption": ""}]}]}]}