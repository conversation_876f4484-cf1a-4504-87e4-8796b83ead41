// <auto-generated/>
global using global::Aspire.Hosting.ApplicationModel;
global using global::Aspire.Hosting.Testing;
global using global::Microsoft.Extensions.DependencyInjection;
global using global::System;
global using global::System.Collections.Generic;
global using global::System.IO;
global using global::System.Linq;
global using global::System.Net;
global using global::System.Net.Http;
global using global::System.Threading;
global using global::System.Threading.Tasks;
global using global::Xunit;
