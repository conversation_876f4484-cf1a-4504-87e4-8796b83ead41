2025-08-05T21:00:40.9724744+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Version: 1.6.2+88f8ce447cd12b629fcfe5e61d80dcc0c8cab8ec
2025-08-05T21:00:40.9851964+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging mode: asynchronous
2025-08-05T21:00:40.9869738+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging level: Information
2025-08-05T21:00:40.9869841+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION CreateBuilderAsync entry time: 21:00:40.927
2025-08-05T21:00:40.9924162+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION PID: 34108
2025-08-05T21:00:40.9924523+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime information: win-x64 - .NET 9.0.7
2025-08-05T21:00:40.9924620+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime location: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.7\System.Private.CoreLib.dll
2025-08-05T21:00:40.9924733+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION IsDynamicCodeSupported: True
2025-08-05T21:00:40.9924763+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Test module: C:\_\Miller_Github\Miller.WMS\Miller.WMS.Tests\bin\Debug\net9.0\Miller.WMS.Tests.dll
2025-08-05T21:00:40.9925440+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Command line arguments: '--server --diagnostic --diagnostic-verbosity Information --diagnostic-output-directory C:\_\Miller_Github\Miller.WMS\Miller.WMS.Tests\bin\Debug\net9.0\Log --results-directory C:\_\Miller_Github\Miller.WMS\TestResults --client-port 62417'
2025-08-05T21:00:40.9940355+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION TESTINGPLATFORM_DEFAULT_HANG_TIMEOUT: ''
2025-08-05T21:00:41.0084637+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting RegisterEnvironmentVariablesConfigurationSource: 'True'
2025-08-05T21:00:41.0228376+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting PlatformExitProcessOnUnhandledException: 'False', config file: False environment variable:  VSTest mode: False
2025-08-05T21:00:41.2013590+00:00 Microsoft.Testing.Platform.Requests.TestHostTestFrameworkInvoker INFORMATION Test framework UID: '30ea7c6e-dd24-4152-a360-1387158cd41d' Version: '2.0.0' DisplayName: 'xUnit.net v3 Microsoft.Testing.Platform test framework' Description: 'xUnit.net v3 Microsoft.Testing.Platform test framework'
2025-08-05T21:00:41.2056400+00:00 xUnit.net INFORMATION xUnit.net v3 In-Process Runner v2.0.0+229879b765 (64-bit .NET 9.0.7)
2025-08-05T21:00:41.2380520+00:00 xUnit.net INFORMATION   Discovering: Miller.WMS.Tests
2025-08-05T21:00:41.2627773+00:00 xUnit.net INFORMATION   Discovered:  Miller.WMS.Tests
2025-08-05T21:00:41.2781957+00:00 xUnit.net INFORMATION   Starting:    Miller.WMS.Tests
2025-08-05T21:01:05.1196696+00:00 xUnit.net INFORMATION   Finished:    Miller.WMS.Tests
2025-08-05T21:01:05.1273324+00:00 xUnit.net INFORMATION === TEST EXECUTION SUMMARY ===
2025-08-05T21:01:05.1355359+00:00 xUnit.net INFORMATION    Miller.WMS.Tests  Total: 1, Errors: 0, Failed: 0, Skipped: 0, Not Run: 0, Time: 23.844s
