2025-08-05T20:59:32.4287428+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Version: 1.6.2+88f8ce447cd12b629fcfe5e61d80dcc0c8cab8ec
2025-08-05T20:59:32.4396158+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging mode: asynchronous
2025-08-05T20:59:32.4406634+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging level: Information
2025-08-05T20:59:32.4406703+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION CreateBuilderAsync entry time: 20:59:32.393
2025-08-05T20:59:32.4447142+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION PID: 32176
2025-08-05T20:59:32.4447552+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime information: win-x64 - .NET 9.0.7
2025-08-05T20:59:32.4447691+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime location: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.7\System.Private.CoreLib.dll
2025-08-05T20:59:32.4447836+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION IsDynamicCodeSupported: True
2025-08-05T20:59:32.4447884+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Test module: C:\_\Miller_Github\Miller.WMS\Miller.WMS.Tests\bin\Debug\net9.0\Miller.WMS.Tests.dll
2025-08-05T20:59:32.4449163+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Command line arguments: '--server --diagnostic --diagnostic-verbosity Information --diagnostic-output-directory C:\_\Miller_Github\Miller.WMS\Miller.WMS.Tests\bin\Debug\net9.0\Log --results-directory C:\_\Miller_Github\Miller.WMS\TestResults --client-port 62332'
2025-08-05T20:59:32.4466417+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION TESTINGPLATFORM_DEFAULT_HANG_TIMEOUT: ''
2025-08-05T20:59:32.4575954+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting RegisterEnvironmentVariablesConfigurationSource: 'True'
2025-08-05T20:59:32.4741584+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting PlatformExitProcessOnUnhandledException: 'False', config file: False environment variable:  VSTest mode: False
2025-08-05T20:59:32.6529763+00:00 Microsoft.Testing.Platform.Requests.TestHostTestFrameworkInvoker INFORMATION Test framework UID: '30ea7c6e-dd24-4152-a360-1387158cd41d' Version: '2.0.0' DisplayName: 'xUnit.net v3 Microsoft.Testing.Platform test framework' Description: 'xUnit.net v3 Microsoft.Testing.Platform test framework'
2025-08-05T20:59:32.6575487+00:00 xUnit.net INFORMATION xUnit.net v3 In-Process Runner v2.0.0+229879b765 (64-bit .NET 9.0.7)
2025-08-05T20:59:32.6866762+00:00 xUnit.net INFORMATION   Discovering: Miller.WMS.Tests
2025-08-05T20:59:32.7084575+00:00 xUnit.net INFORMATION   Discovered:  Miller.WMS.Tests
2025-08-05T20:59:32.7219784+00:00 xUnit.net INFORMATION   Starting:    Miller.WMS.Tests
2025-08-05T20:59:56.4343976+00:00 xUnit.net ERROR     Miller.WMS.Tests.WebTests.GetWebResourceRootReturnsOkStatusCode [FAIL]
2025-08-05T20:59:56.4349114+00:00 xUnit.net INFORMATION       Polly.Timeout.TimeoutRejectedException : The operation didn't complete within the allowed timeout of '00:00:10'.
2025-08-05T20:59:56.4349261+00:00 xUnit.net INFORMATION       ---- System.Threading.Tasks.TaskCanceledException : A task was canceled.
2025-08-05T20:59:56.4354555+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-05T20:59:56.4360998+00:00 xUnit.net INFORMATION            at Polly.Utils.ExceptionUtilities.TrySetStackTrace[T](T exception)
2025-08-05T20:59:56.4361209+00:00 xUnit.net INFORMATION            at Polly.Timeout.TimeoutResilienceStrategy.ExecuteCore[TResult,TState](Func`3 callback, ResilienceContext context, TState state)
2025-08-05T20:59:56.4361397+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
2025-08-05T20:59:56.4361489+00:00 xUnit.net INFORMATION            at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
2025-08-05T20:59:56.4361592+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
2025-08-05T20:59:56.4361676+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
2025-08-05T20:59:56.4361771+00:00 xUnit.net INFORMATION            at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
2025-08-05T20:59:56.4361838+00:00 xUnit.net INFORMATION            at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
2025-08-05T20:59:56.4361893+00:00 xUnit.net INFORMATION            at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
2025-08-05T20:59:56.4361992+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
2025-08-05T20:59:56.4362100+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncValueTaskMethodBuilder`1.SetResult(TResult result)
2025-08-05T20:59:56.4362210+00:00 xUnit.net INFORMATION            at Polly.Utils.StrategyHelper.<ExecuteCallbackSafeAsync>g__AwaitTask|0_0[TResult,TState,T](ValueTask`1 task, Boolean continueOnCapturedContext)
2025-08-05T20:59:56.4362306+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
2025-08-05T20:59:56.4362519+00:00 xUnit.net INFORMATION            at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
2025-08-05T20:59:56.4362625+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
2025-08-05T20:59:56.4362702+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
2025-08-05T20:59:56.4362783+00:00 xUnit.net INFORMATION            at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
2025-08-05T20:59:56.4362842+00:00 xUnit.net INFORMATION            at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
2025-08-05T20:59:56.4362910+00:00 xUnit.net INFORMATION            at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
2025-08-05T20:59:56.4362987+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
2025-08-05T20:59:56.4363053+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncValueTaskMethodBuilder`1.SetResult(TResult result)
2025-08-05T20:59:56.4363151+00:00 xUnit.net INFORMATION            at Polly.ResiliencePipeline.<>c.<<ExecuteAsync>b__3_0>d.MoveNext()
2025-08-05T20:59:56.4363234+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
2025-08-05T20:59:56.4363328+00:00 xUnit.net INFORMATION            at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
2025-08-05T20:59:56.4363413+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
2025-08-05T20:59:56.4363710+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
2025-08-05T20:59:56.4363867+00:00 xUnit.net INFORMATION            at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
2025-08-05T20:59:56.4363980+00:00 xUnit.net INFORMATION            at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
2025-08-05T20:59:56.4364252+00:00 xUnit.net INFORMATION            at System.Threading.Tasks.Task.TrySetCanceled(CancellationToken tokenToRecord, Object cancellationException)
2025-08-05T20:59:56.4364337+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetException(Exception exception, Task`1& taskField)
2025-08-05T20:59:56.4364722+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpExecutor.cs(644,0): at Aspire.Hosting.Dcp.DcpExecutor.<>c__DisplayClass54_0.<<CreateServicesAsync>b__1>d.MoveNext()
2025-08-05T20:59:56.4364830+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
2025-08-05T20:59:56.4364995+00:00 xUnit.net INFORMATION            at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
2025-08-05T20:59:56.4365090+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
2025-08-05T20:59:56.4365166+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
2025-08-05T20:59:56.4365308+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/KubernetesService.cs(275,0): at Aspire.Hosting.Dcp.KubernetesService.WatchAsync[T](String namespaceParameter, CancellationToken cancellationToken)+MoveNext()
2025-08-05T20:59:56.4365411+00:00 xUnit.net INFORMATION            at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
2025-08-05T20:59:56.4365498+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
2025-08-05T20:59:56.4365639+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Utils/PeriodicRestartAsyncEnumerable.cs(59,0): at Aspire.Hosting.Utils.PeriodicRestartAsyncEnumerable.CreateAsync[T](Func`3 enumerableFactory, TimeSpan restartInterval, CancellationToken cancellationToken)+MoveNext()
2025-08-05T20:59:56.4365759+00:00 xUnit.net INFORMATION            at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
2025-08-05T20:59:56.4365846+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
2025-08-05T20:59:56.4365944+00:00 xUnit.net INFORMATION            at k8s.Watcher`1.CreateWatchEventEnumerator(Func`1 streamReaderCreator, Action`1 onError, CancellationToken cancellationToken)+MoveNext()
2025-08-05T20:59:56.4366032+00:00 xUnit.net INFORMATION            at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
2025-08-05T20:59:56.4366118+00:00 xUnit.net INFORMATION            at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
2025-08-05T20:59:56.4366204+00:00 xUnit.net INFORMATION            at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
2025-08-05T20:59:56.4366268+00:00 xUnit.net INFORMATION            at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
2025-08-05T20:59:56.4366436+00:00 xUnit.net INFORMATION            at System.Threading.Tasks.Task.InternalCancel()
2025-08-05T20:59:56.4366522+00:00 xUnit.net INFORMATION            at System.Threading.CancellationTokenSource.Invoke(Delegate d, Object state, CancellationTokenSource source)
2025-08-05T20:59:56.4366596+00:00 xUnit.net INFORMATION            at System.Threading.CancellationTokenSource.ExecuteCallbackHandlers(Boolean throwOnFirstException)
2025-08-05T20:59:56.4366677+00:00 xUnit.net INFORMATION            at System.Threading.CancellationTokenSource.Invoke(Delegate d, Object state, CancellationTokenSource source)
2025-08-05T20:59:56.4366750+00:00 xUnit.net INFORMATION            at System.Threading.CancellationTokenSource.ExecuteCallbackHandlers(Boolean throwOnFirstException)
2025-08-05T20:59:56.4366802+00:00 xUnit.net INFORMATION            at System.Threading.TimerQueueTimer.Fire(Boolean isThreadPool)
2025-08-05T20:59:56.4366848+00:00 xUnit.net INFORMATION            at System.Threading.TimerQueue.FireNextTimers()
2025-08-05T20:59:56.4366895+00:00 xUnit.net INFORMATION            at System.Threading.ThreadPoolWorkQueue.Dispatch()
2025-08-05T20:59:56.4366953+00:00 xUnit.net INFORMATION            at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
2025-08-05T20:59:56.4366982+00:00 xUnit.net INFORMATION         --- End of stack trace from previous location ---
2025-08-05T20:59:56.4367023+00:00 xUnit.net INFORMATION            at Polly.Outcome`1.GetResultOrRethrow()
2025-08-05T20:59:56.4367097+00:00 xUnit.net INFORMATION            at Polly.ResiliencePipeline.ExecuteAsync(Func`2 callback, CancellationToken cancellationToken)
2025-08-05T20:59:56.4367266+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpExecutor.cs(641,0): at Aspire.Hosting.Dcp.DcpExecutor.CreateServicesAsync(CancellationToken cancellationToken)
2025-08-05T20:59:56.4367360+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpExecutor.cs(127,0): at Aspire.Hosting.Dcp.DcpExecutor.RunApplicationAsync(CancellationToken cancellationToken)
2025-08-05T20:59:56.4367456+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Orchestrator/ApplicationOrchestrator.cs(316,0): at Aspire.Hosting.Orchestrator.ApplicationOrchestrator.RunApplicationAsync(CancellationToken cancellationToken)
2025-08-05T20:59:56.4367562+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Orchestrator/OrchestratorHostService.cs(41,0): at Aspire.Hosting.Orchestrator.OrchestratorHostService.StartAsync(CancellationToken cancellationToken)
2025-08-05T20:59:56.4367704+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)
2025-08-05T20:59:56.4367841+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-08-05T20:59:56.4367918+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
2025-08-05T20:59:56.4368010+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationFactory.cs(616,0): at Aspire.Hosting.Testing.DistributedApplicationFactory.ObservedHost.StartAsync(CancellationToken cancellationToken)
2025-08-05T20:59:56.4368100+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationFactory.cs(74,0): at Aspire.Hosting.Testing.DistributedApplicationFactory.StartAsync(CancellationToken cancellationToken)
2025-08-05T20:59:56.4368362+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationTestingBuilder.cs(284,0): at Aspire.Hosting.Testing.DistributedApplicationTestingBuilder.SuspendingDistributedApplicationFactory.DelegatedHost.StartAsync(CancellationToken cancellationToken)
2025-08-05T20:59:56.4368477+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationTestingBuilder.cs(259,0): at Aspire.Hosting.Testing.DistributedApplicationTestingBuilder.SuspendingDistributedApplicationFactory.DelegatedDistributedApplication.StartAsync(CancellationToken cancellationToken)
2025-08-05T20:59:56.4368572+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Tests\WebTests.cs(30,0): at Miller.WMS.Tests.WebTests.GetWebResourceRootReturnsOkStatusCode()
2025-08-05T20:59:56.4368706+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Tests\WebTests.cs(38,0): at Miller.WMS.Tests.WebTests.GetWebResourceRootReturnsOkStatusCode()
2025-08-05T20:59:56.4368733+00:00 xUnit.net INFORMATION         --- End of stack trace from previous location ---
2025-08-05T20:59:56.4368753+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-05T20:59:56.4368851+00:00 xUnit.net INFORMATION            at k8s.Watcher`1.CreateWatchEventEnumerator(Func`1 streamReaderCreator, Action`1 onError, CancellationToken cancellationToken)+MoveNext()
2025-08-05T20:59:56.4368983+00:00 xUnit.net INFORMATION            at k8s.Watcher`1.CreateWatchEventEnumerator(Func`1 streamReaderCreator, Action`1 onError, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
2025-08-05T20:59:56.4369075+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Utils/PeriodicRestartAsyncEnumerable.cs(39,0): at Aspire.Hosting.Utils.PeriodicRestartAsyncEnumerable.CreateAsync[T](Func`3 enumerableFactory, TimeSpan restartInterval, CancellationToken cancellationToken)+MoveNext()
2025-08-05T20:59:56.4369288+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Utils/PeriodicRestartAsyncEnumerable.cs(59,0): at Aspire.Hosting.Utils.PeriodicRestartAsyncEnumerable.CreateAsync[T](Func`3 enumerableFactory, TimeSpan restartInterval, CancellationToken cancellationToken)+MoveNext()
2025-08-05T20:59:56.4369450+00:00 xUnit.net INFORMATION            at Aspire.Hosting.Utils.PeriodicRestartAsyncEnumerable.CreateAsync[T](Func`3 enumerableFactory, TimeSpan restartInterval, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
2025-08-05T20:59:56.4369534+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/KubernetesService.cs(275,0): at Aspire.Hosting.Dcp.KubernetesService.WatchAsync[T](String namespaceParameter, CancellationToken cancellationToken)+MoveNext()
2025-08-05T20:59:56.4369663+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/KubernetesService.cs(275,0): at Aspire.Hosting.Dcp.KubernetesService.WatchAsync[T](String namespaceParameter, CancellationToken cancellationToken)+MoveNext()
2025-08-05T20:59:56.4369803+00:00 xUnit.net INFORMATION            at Aspire.Hosting.Dcp.KubernetesService.WatchAsync[T](String namespaceParameter, CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
2025-08-05T20:59:56.4369883+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpExecutor.cs(644,0): at Aspire.Hosting.Dcp.DcpExecutor.<>c__DisplayClass54_0.<<CreateServicesAsync>b__1>d.MoveNext()
2025-08-05T20:59:56.4369908+00:00 xUnit.net INFORMATION         --- End of stack trace from previous location ---
2025-08-05T20:59:56.4369985+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpExecutor.cs(644,0): at Aspire.Hosting.Dcp.DcpExecutor.<>c__DisplayClass54_0.<<CreateServicesAsync>b__1>d.MoveNext()
2025-08-05T20:59:56.4370009+00:00 xUnit.net INFORMATION         --- End of stack trace from previous location ---
2025-08-05T20:59:56.4370099+00:00 xUnit.net INFORMATION            at Polly.ResiliencePipeline.<>c.<<ExecuteAsync>b__3_0>d.MoveNext()
2025-08-05T20:59:56.4972086+00:00 xUnit.net INFORMATION   Finished:    Miller.WMS.Tests
2025-08-05T20:59:56.5036881+00:00 xUnit.net INFORMATION === TEST EXECUTION SUMMARY ===
2025-08-05T20:59:56.5190951+00:00 xUnit.net INFORMATION    Miller.WMS.Tests  Total: 1, Errors: 0, Failed: 1, Skipped: 0, Not Run: 0, Time: 23.777s
