{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["2Uu5rsL3UwsEX3gOceh2I9IKV+c7MVfuq6L1ACpP9yE=", "5tgvXaenwxk/633qIzccozUQfxpgl2tM169Do32rtYA=", "2Mk7zMoOosHZl032UcFV5Zy0+NOds3suCYoDMWPn3V4=", "riLgfPJ3GnuyqbuLzNlHe9ikc2DXg5VWD7+JYC39408=", "+sxdrvMeFYPKK5rFF7bHQ8pAFppSWH9sAMdHaNupU0Y=", "jjb6Dq0uUL57xEf4oIdJnmTUPJrGNGPKAqSGe1LFqLs=", "yv2rU7dlb9L+TpLtQF3IcVfgu2fR+K5iN0htyJijzH0=", "KTj8Rbb6zpTewamN9WKZ1nvDieZLikir+5+tMC2lnwk=", "9wuvhZD8jNUUkGicbd5AJgp7a9UIWKfDs1ZPj5A+PlA=", "GAjxIk580MVU/cRUmaWm9rZLUn5q15T5j4UkBJyW1LI=", "8WZJgvuLvkvZLx0ECcfiujUbxCKNv9lKEWSRgh6Ksys=", "Wk7MxenrXEVhwRVj/MmONC51AUoZId14T7EVXrLc1hk=", "iMkd2lTYfrscQ3i/w6JhMsLc67cakRO93OQOjFvNRv8=", "tnngps4u1SaBegy+UsMPiwLL+7hteFKk7myzxUgUXTA=", "HFgkVt5xpm/u/7QjXkW7MhfMXr5lEHvnoe9YwzbEg+E=", "bOtXcxu+/oRaEetHAGRtDsSpszsDrFnUGOASUfLilhw=", "aoulEh92XpyGXa3icz/P2Y/L+ZqGxX/+7L6vqPu3iXw=", "qReurTVzMOtXDCZEr/s4Lbl3XIG1XDutkcfZkRWX/HI=", "zTd6YH/02H0hT9Br3Up3ADEq4KvYE1Do3qSV0Svia+c=", "/y+sT7dZIqZsovghL1mmI67x5GlOipOHmNTXhmvd/0k=", "1G0Wij1ikEPQ9xrf7J3PCyCROtoWrIAEFGp+0uSZvOs=", "8XeMCzD79H097Gt4NQ9tU57cwQLMzg6vGJsihncNRNk=", "+mRrVHcG2/e2kxl+Az/BLIM3Q5weQsGCuaKBcWtFzNs=", "SxH1CrwL5iI2Cp3xjugEIHfcKa7m5e1imtTOirC4EnI=", "iX7DwMSoE8sVdgep01oc5OZ2krLon+X2PZYDzMDsnnE=", "va/p/ZxvMcKpLJalrY7GDkSNQzmAjZ2m7/i6p1FZMGo=", "SISWh578WH0Z6Jt2SQ8uCGI3Xa5xP2Ormw04PqfiaQo=", "5i0kCrFKOXXPAVxYaQRLK2kguuoFtXo9HQWQdGBrQiA=", "oWkyQl8f9+j7tR5ztypvS3XYmZmvA8JGq0A63ClC8Ho=", "vvf20n3wY5KmWek7cRj0C6mnOj0HCoUf5RrdMBZLg+c=", "VmfvljYpbJ92AjdTRIP1mAbYZm1r41Or/34R2n94CC0=", "IBODdMPQPhbOouzquHW8/iHNCX5wh0HuvtGc9MVcCZE=", "xQC3Bu+LtVzv5ZJFyX4YnKFyk2QM01fBTbzy4UQFTas=", "kxEYEzKuN4IkEBVuhtYfaDZqm5PzLVRdqsFfuZTqVsQ=", "ItfMFTs/Fx2TQnhoWMh3mcJLzxzzC76KX25EVCgZxbI=", "/fkiuzIqeBCvPAaSZ0mSzdVFzjSw+jYXk/VvsnkmMqI=", "vXycWbqCO0A5T78gbFyb3QSSKUu4CdzroGGaLbtgYcE=", "jVRycSqC0k61V9bGYjaGx1ZkmoxWCnogbDKs9b8x7T4=", "mUAUshMFu12yliqLiJVNTZWDx2gapGysa+fVp2Nb1uU=", "hqDbFl9re5PYQSRxeeg4+0P3g2iJ+GKvuzguvQ63YCc=", "BOmx5Y68t3VmF0w24iNarn4Ub0AeSQqP1dsFIlEDleU=", "Wq1GCFqKP+3k+sLQENc0jHObgmESj6MR24/dIBPGvRg=", "pUjUrUniqUnj5YUAl9uDNNFWXQ6xDajGO6NDjNmvJ5E=", "4UqKabrY9n4zYwo5Mtl/y1jPYOYRVRt5/g2YBrpL1z8=", "WbfMmNwv90CI7uivYQ74j6R9nFj4NsGqyOoomULZPsM=", "QRMDn8HkY5IPEkNntVD6Lcbuv+k9rn6whEmR17rUzFA=", "z+lqJLzcNRiS0uVsMj/QbWL7nbehyZ6kfbSV5t1nfK4="], "CachedAssets": {"2Uu5rsL3UwsEX3gOceh2I9IKV+c7MVfuq6L1ACpP9yE=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\sll4euyegn-d3h8l9wove.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "app#[.{fingerprint=d3h8l9wove}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fcjvn85lhy", "Integrity": "UWWnUOvQ8BaUL2Mrm0bFRrZ6+G9ohT6UBCOo1O347Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\app.css", "FileLength": 1511, "LastWriteTime": "2025-08-05T20:55:56.8672872+00:00"}, "5tgvXaenwxk/633qIzccozUQfxpgl2tM169Do32rtYA=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\tf4cmeivg9-t1cqhe9u97.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=t1cqhe9u97}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4t5zwq0ztq", "Integrity": "KJApOSMW9VEyvj1vwAXL6xmmIowXietNt8eeRzZOMrI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6973, "LastWriteTime": "2025-08-05T20:55:56.8852619+00:00"}, "2Mk7zMoOosHZl032UcFV5Zy0+NOds3suCYoDMWPn3V4=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\7xm08l4ixn-c2jlpeoesf.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bpa5g0wlhg", "Integrity": "keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 33080, "LastWriteTime": "2025-08-05T20:55:56.8927766+00:00"}, "riLgfPJ3GnuyqbuLzNlHe9ikc2DXg5VWD7+JYC39408=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\giyfa7et8b-sejl45xvog.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=sejl45xvog}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ru0tedr80l", "Integrity": "k3viZHtAtH1nURlGUHDkCuzFiiGm9hugvlQpwCBT5nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 6106, "LastWriteTime": "2025-08-05T20:55:56.8682874+00:00"}, "+sxdrvMeFYPKK5rFF7bHQ8pAFppSWH9sAMdHaNupU0Y=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\lndg2s5im1-aexeepp0ev.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3latwtrz94", "Integrity": "1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 14072, "LastWriteTime": "2025-08-05T20:55:56.880598+00:00"}, "jjb6Dq0uUL57xEf4oIdJnmTUPJrGNGPKAqSGe1LFqLs=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\6lcsfls6zf-xvp3kq03qx.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=xvp3kq03qx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z7y6dfz9j3", "Integrity": "vysQoe3FbcQ93VMRtTh9noOgIS/zfHwqe4MpEegkmNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6975, "LastWriteTime": "2025-08-05T20:55:56.8852619+00:00"}, "yv2rU7dlb9L+TpLtQF3IcVfgu2fR+K5iN0htyJijzH0=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\x8ftxzgmlw-ausgxo2sd3.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnst782kog", "Integrity": "ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 33061, "LastWriteTime": "2025-08-05T20:55:56.975016+00:00"}, "KTj8Rbb6zpTewamN9WKZ1nvDieZLikir+5+tMC2lnwk=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\c6bkz3byyv-22vffe00uq.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=22vffe00uq}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ki2uzdiv5", "Integrity": "cWQujyWwtYpOrAL/BVrmbuQuLebSh+MK7RHT7reHPL0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 6108, "LastWriteTime": "2025-08-05T20:55:56.8907671+00:00"}, "9wuvhZD8jNUUkGicbd5AJgp7a9UIWKfDs1ZPj5A+PlA=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zcnnnh36ui-cosvhxvwiu.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f178oapzb7", "Integrity": "XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 14093, "LastWriteTime": "2025-08-05T20:55:56.901777+00:00"}, "GAjxIk580MVU/cRUmaWm9rZLUn5q15T5j4UkBJyW1LI=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\j9hje8ybqm-qesaa3a1fm.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=qesaa3a1fm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yhemft0x44", "Integrity": "GsqAqFf0gorXCNSLhkvLgjTUzhvnj/r/ziu5TKmgRn8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3406, "LastWriteTime": "2025-08-05T20:55:56.8672872+00:00"}, "8WZJgvuLvkvZLx0ECcfiujUbxCKNv9lKEWSRgh6Ksys=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\w4wnu48mx3-fvhpjtyr6v.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5b7ig2cj79", "Integrity": "fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25714, "LastWriteTime": "2025-08-05T20:55:56.8907671+00:00"}, "Wk7MxenrXEVhwRVj/MmONC51AUoZId14T7EVXrLc1hk=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\clxqqlagr5-tmc1g35s3z.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=tmc1g35s3z}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8wl3mrbh96", "Integrity": "mKOek+hcGxc9JNCDfWknxZ1k1HnVlpIzrAjn/7Qovgc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3204, "LastWriteTime": "2025-08-05T20:55:56.9007784+00:00"}, "iMkd2lTYfrscQ3i/w6JhMsLc67cakRO93OQOjFvNRv8=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\awa0pufkqj-fsbi9cje9m.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ggfb0v5ylw", "Integrity": "ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12568, "LastWriteTime": "2025-08-05T20:55:56.8682874+00:00"}, "tnngps4u1SaBegy+UsMPiwLL+7hteFKk7myzxUgUXTA=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\anq611cgxs-rxsg74s51o.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rxsg74s51o}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fjx614p1f2", "Integrity": "tQ/ZHQ02xDDr7P2Fg/W0cSQUbq1b8IQ7Xs+98LoDdrI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3395, "LastWriteTime": "2025-08-05T20:55:56.8745535+00:00"}, "HFgkVt5xpm/u/7QjXkW7MhfMXr5lEHvnoe9YwzbEg+E=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\vxrqzkb88z-ee0r1s7dh0.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kqk14ew2nl", "Integrity": "PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25726, "LastWriteTime": "2025-08-05T20:55:56.8822516+00:00"}, "bOtXcxu+/oRaEetHAGRtDsSpszsDrFnUGOASUfLilhw=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\mkubolfew3-q9ht133ko3.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=q9ht133ko3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d645a0veuj", "Integrity": "ZV74uw9avCkAGQbxeK2wfps8314gun6xEJG4X6KsOXY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3235, "LastWriteTime": "2025-08-05T20:55:56.8712877+00:00"}, "aoulEh92XpyGXa3icz/P2Y/L+ZqGxX/+7L6vqPu3iXw=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\t7jrmhh9j1-jd9uben2k1.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ldvkm706vq", "Integrity": "J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15028, "LastWriteTime": "2025-08-05T20:55:56.8765735+00:00"}, "qReurTVzMOtXDCZEr/s4Lbl3XIG1XDutkcfZkRWX/HI=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\3wy4uav4yr-gye83jo8yx.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=gye83jo8yx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g3pw7iimyz", "Integrity": "DNbwelsqgYZDvJGAT2jwEYUmCQ08fVNGjkxqe572kmk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 12244, "LastWriteTime": "2025-08-05T20:55:56.883264+00:00"}, "zTd6YH/02H0hT9Br3Up3ADEq4KvYE1Do3qSV0Svia+c=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\1y5rswsd3k-r4e9w2rdcm.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21g01g2f66", "Integrity": "JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44266, "LastWriteTime": "2025-08-05T20:55:56.8917747+00:00"}, "/y+sT7dZIqZsovghL1mmI67x5GlOipOHmNTXhmvd/0k=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\et9hy8paoo-wl58j5mj3v.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=wl58j5mj3v}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2dzz7zk1je", "Integrity": "Og/4AkcNfLOxVj/DmATc4F18q6RjYgz/OrjtVYYkZTQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11153, "LastWriteTime": "2025-08-05T20:55:56.8872634+00:00"}, "1G0Wij1ikEPQ9xrf7J3PCyCROtoWrIAEFGp+0uSZvOs=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\y1nec1zgnj-c2oey78nd0.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oiolxl3gck", "Integrity": "oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24487, "LastWriteTime": "2025-08-05T20:55:56.908779+00:00"}, "8XeMCzD79H097Gt4NQ9tU57cwQLMzg6vGJsihncNRNk=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\fsqqj96pld-d4r6k3f320.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=d4r6k3f320}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a9e8v2fr2b", "Integrity": "0o55tA+sg7mY2XRLNBCJpRjgbMaoOXFrmEpYn5eaikg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 12194, "LastWriteTime": "2025-08-05T20:55:56.8702876+00:00"}, "+mRrVHcG2/e2kxl+Az/BLIM3Q5weQsGCuaKBcWtFzNs=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\qkk0jggjnv-j5mq2jizvt.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pnmw3mh9ht", "Integrity": "tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44237, "LastWriteTime": "2025-08-05T20:55:56.8957783+00:00"}, "SxH1CrwL5iI2Cp3xjugEIHfcKa7m5e1imtTOirC4EnI=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\dj9hlm0kzn-keugtjm085.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=keugtjm085}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b271r4kg0j", "Integrity": "A6WMzqJSc0ku4mP+TCXA3zO5Xwz4hHan7U/eu325Y4A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11132, "LastWriteTime": "2025-08-05T20:55:56.9687226+00:00"}, "iX7DwMSoE8sVdgep01oc5OZ2krLon+X2PZYDzMDsnnE=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\cu47vy4gfj-nvvlpmu67g.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0gru265j2n", "Integrity": "cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24456, "LastWriteTime": "2025-08-05T20:55:56.9730055+00:00"}, "va/p/ZxvMcKpLJalrY7GDkSNQzmAjZ2m7/i6p1FZMGo=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\tw7pasprv0-zub09dkrxp.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=zub09dkrxp}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ndo96zskmb", "Integrity": "q9WZtmMnJ+oJNgmN0eeT5IvKsKV1/YleuDnjGVjv4ps=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33770, "LastWriteTime": "2025-08-05T20:55:56.9057794+00:00"}, "SISWh578WH0Z6Jt2SQ8uCGI3Xa5xP2Ormw04PqfiaQo=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\hfcgtdi7p3-pj5nd1wqec.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glev5uv9kg", "Integrity": "pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 114902, "LastWriteTime": "2025-08-05T20:55:56.9871295+00:00"}, "5i0kCrFKOXXPAVxYaQRLK2kguuoFtXo9HQWQdGBrQiA=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\xmyml8ppsk-43atpzeawx.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=43atpzeawx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ny7oqyylde", "Integrity": "sT/qLvBmXOeeXFjXr+qriR/tEhjqoanwO9cF0pJ6+Yk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 31123, "LastWriteTime": "2025-08-05T20:55:56.8957783+00:00"}, "oWkyQl8f9+j7tR5ztypvS3XYmZmvA8JGq0A63ClC8Ho=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\scf3j3m3nr-v0zj4ognzu.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77t5enldb7", "Integrity": "Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91960, "LastWriteTime": "2025-08-05T20:55:56.9227798+00:00"}, "vvf20n3wY5KmWek7cRj0C6mnOj0HCoUf5RrdMBZLg+c=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\ujxopsqs3c-ynyaa8k90p.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=ynyaa8k90p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ws8e397h8g", "Integrity": "EHv6Fxyfhm4sQaIznaio1clpsqF/v/3Le3ecZHQESCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33667, "LastWriteTime": "2025-08-05T20:55:56.944859+00:00"}, "VmfvljYpbJ92AjdTRIP1mAbYZm1r41Or/34R2n94CC0=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\5rxbwjmfei-hrwsygsryq.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2knqut3gi", "Integrity": "YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114877, "LastWriteTime": "2025-08-05T20:55:56.9465586+00:00"}, "IBODdMPQPhbOouzquHW8/iHNCX5wh0HuvtGc9MVcCZE=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\ygu81wg92r-c63t5i9ira.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=c63t5i9ira}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a66bztmsz2", "Integrity": "AuvSI7nlwZgOHR4lTv48WmlYy8v4vpvtnbAtTSHGmTQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 31142, "LastWriteTime": "2025-08-05T20:55:56.9137796+00:00"}, "xQC3Bu+LtVzv5ZJFyX4YnKFyk2QM01fBTbzy4UQFTas=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zrm5bgsn2i-ft3s53vfgj.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uom9ela1zq", "Integrity": "4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91866, "LastWriteTime": "2025-08-05T20:55:56.9807567+00:00"}, "kxEYEzKuN4IkEBVuhtYfaDZqm5PzLVRdqsFfuZTqVsQ=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\z5dgsvywh2-iy2auvubsp.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=iy2auvubsp}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42jbnex9ve", "Integrity": "PPRimh2a7UdzoAH+CvP1whLwkhlzs0R8qUK8MPWML2Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 54528, "LastWriteTime": "2025-08-05T20:55:56.9405094+00:00"}, "ItfMFTs/Fx2TQnhoWMh3mcJLzxzzC76KX25EVCgZxbI=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\q03xumkmu6-fxquxrv84i.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=fxquxrv84i}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xhofi<PERSON><PERSON>", "Integrity": "xUN1UfXISZUIRgFENmQNtzFAJfWc0jpSWPRdfTjARpM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92278, "LastWriteTime": "2025-08-05T20:55:56.9472412+00:00"}, "/fkiuzIqeBCvPAaSZ0mSzdVFzjSw+jYXk/VvsnkmMqI=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\pywypbh9qv-252a5wndhh.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=252a5wndhh}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ktya8jz5ov", "Integrity": "yHJ5EJwF8Y0lQ8hjR9PSjERAHVR5TWbBlCKMsKA/Txs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 32944, "LastWriteTime": "2025-08-05T20:55:56.9790373+00:00"}, "vXycWbqCO0A5T78gbFyb3QSSKUu4CdzroGGaLbtgYcE=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\q4jj7tbtng-okq9zf051y.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=okq9zf051y}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3zm0g72duv", "Integrity": "T2CWaRF2o4EKi5SNbaW3sd3m7mKag5LAdw/EdDU9S58=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86520, "LastWriteTime": "2025-08-05T20:55:56.8967775+00:00"}, "jVRycSqC0k61V9bGYjaGx1ZkmoxWCnogbDKs9b8x7T4=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\g82huob31c-ja11lcg8ur.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=ja11lcg8ur}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i8nhkk7sb8", "Integrity": "HpyKGSNAJmvFW2JQ2rr+8pCNjbm/lCjXKEM1Hm6hFyc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 38013, "LastWriteTime": "2025-08-05T20:55:56.9465586+00:00"}, "mUAUshMFu12yliqLiJVNTZWDx2gapGysa+fVp2Nb1uU=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\9eonhfmzpl-wf6sfai52w.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=wf6sfai52w}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ktgldfzmo5", "Integrity": "aMHA9ZETcWIScTmE/qW8GnAB6Vq3bPkoZzYb2CIqDn8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64236, "LastWriteTime": "2025-08-05T20:55:56.9934872+00:00"}, "hqDbFl9re5PYQSRxeeg4+0P3g2iJ+GKvuzguvQ63YCc=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\1bpdowd3ur-n5tfi6zt97.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=n5tfi6zt97}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fd8at7e5rf", "Integrity": "Hd51/ICuZDXnORaSusIoHCgUhhYiqlw+xpRZYL70fuY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 28200, "LastWriteTime": "2025-08-05T20:55:56.9364859+00:00"}, "BOmx5Y68t3VmF0w24iNarn4Ub0AeSQqP1dsFIlEDleU=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\2aijt2o6r4-eve9uzuztn.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=eve9uzuztn}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "43czolro0z", "Integrity": "EJxn9g8lywGEK14NsSRZd2K3+UXEihxQuQQ9RrZuoJM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56387, "LastWriteTime": "2025-08-05T20:55:56.944859+00:00"}, "Wq1GCFqKP+3k+sLQENc0jHObgmESj6MR24/dIBPGvRg=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\g3m6bm0i7l-cwuvm2sdc3.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=cwuvm2sdc3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3uebi1j6we", "Integrity": "28V7g48B1eUrV0CJSnJvCle1DISFctbwHTG261ZK06k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 38707, "LastWriteTime": "2025-08-05T20:55:56.9847717+00:00"}, "pUjUrUniqUnj5YUAl9uDNNFWXQ6xDajGO6NDjNmvJ5E=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\kbig5l79ed-k72fsduyas.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=k72fsduyas}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f8y9r0oetv", "Integrity": "ce96TscBDwGaFjkGnwgZLT7xSSHzsiTTGlqMq4K/oSo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64493, "LastWriteTime": "2025-08-05T20:55:56.9147797+00:00"}, "4UqKabrY9n4zYwo5Mtl/y1jPYOYRVRt5/g2YBrpL1z8=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\qjz6howncd-ze3dr5b7df.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=ze3dr5b7df}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2kg58l0pnq", "Integrity": "Ep8TXPdKe1BRQmMaiwjzGLpqIqCsa8JeziGfy91QOfU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 26285, "LastWriteTime": "2025-08-05T20:55:56.9339357+00:00"}, "WbfMmNwv90CI7uivYQ74j6R9nFj4NsGqyOoomULZPsM=": {"Identity": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\zn1yxmpder-r37jpkscte.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=r37jpkscte}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1vtrgsyhbw", "Integrity": "E9AKsWN43PnjF/JB5K3R3IOVOy282C8xJ7j67Ywpn9Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55527, "LastWriteTime": "2025-08-05T20:55:56.9770267+00:00"}, "QRMDn8HkY5IPEkNntVD6Lcbuv+k9rn6whEmR17rUzFA=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\anlqmrsivh-t6ifcaearb.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Computed", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "Miller.WMS.Web#[.{fingerprint=t6ifcaearb}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Miller.WMS.Web.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mwsowy9c7o", "Integrity": "ViCpKwXJbTh6AF3wUPCXq8Pymvoi1RewPislSiGnlWI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Miller.WMS.Web.styles.css", "FileLength": 1729, "LastWriteTime": "2025-08-05T20:55:56.9770267+00:00"}, "z+lqJLzcNRiS0uVsMj/QbWL7nbehyZ6kfbSV5t1nfK4=": {"Identity": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\lebqv9thpd-t6ifcaearb.gz", "SourceId": "Miller.WMS.Web", "SourceType": "Computed", "ContentRoot": "C:\\_\\<PERSON>_<PERSON>ithub\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Miller.WMS.Web", "RelativePath": "Miller.WMS.Web#[.{fingerprint=t6ifcaearb}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Miller.WMS.Web.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mwsowy9c7o", "Integrity": "ViCpKwXJbTh6AF3wUPCXq8Pymvoi1RewPislSiGnlWI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_\\<PERSON>_Github\\Miller.WMS\\Miller.WMS.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Miller.WMS.Web.bundle.scp.css", "FileLength": 1729, "LastWriteTime": "2025-08-05T20:55:56.9384977+00:00"}}, "CachedCopyCandidates": {}}