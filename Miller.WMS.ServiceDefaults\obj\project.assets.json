{"version": 3, "targets": {"net9.0": {"Microsoft.Extensions.AmbientMetadata.Application/9.4.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Compliance.Abstractions/9.4.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.ObjectPool": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.4.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.4.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Features/8.0.15": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Features.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Features.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Http/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Http.Diagnostics/9.4.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "9.4.0", "Microsoft.Extensions.Http": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4", "Microsoft.Extensions.Telemetry": "9.4.0", "System.IO.Pipelines": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Http.Resilience/9.4.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.Http.Diagnostics": "9.4.0", "Microsoft.Extensions.ObjectPool": "9.0.4", "Microsoft.Extensions.Resilience": "9.4.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Http.Resilience.targets": {}}}, "Microsoft.Extensions.Logging/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Configuration/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.ObjectPool/9.0.4": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Primitives/9.0.4": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Resilience/9.4.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Diagnostics": "9.0.4", "Microsoft.Extensions.Diagnostics.ExceptionSummarization": "9.4.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4", "Microsoft.Extensions.Telemetry.Abstractions": "9.4.0", "Polly.Extensions": "8.4.2", "Polly.RateLimiting": "8.4.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.ServiceDiscovery/9.3.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Features": "8.0.15", "Microsoft.Extensions.Http": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0", "Microsoft.Extensions.ServiceDiscovery.Abstractions": "9.3.1"}, "compile": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.dll": {"related": ".xml"}}}, "Microsoft.Extensions.ServiceDiscovery.Abstractions/9.3.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Features": "8.0.15", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Telemetry/9.4.0": {"type": "package", "dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "9.4.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "9.4.0", "Microsoft.Extensions.Logging.Configuration": "9.0.4", "Microsoft.Extensions.ObjectPool": "9.0.4", "Microsoft.Extensions.Telemetry.Abstractions": "9.4.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Telemetry.Abstractions/9.4.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "9.4.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.ObjectPool": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Telemetry.Abstractions.props": {}, "buildTransitive/net8.0/Microsoft.Extensions.Telemetry.Abstractions.targets": {}}}, "OpenTelemetry/1.12.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Diagnostics.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Configuration": "9.0.0", "OpenTelemetry.Api.ProviderBuilderExtensions": "1.12.0"}, "compile": {"lib/net9.0/OpenTelemetry.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}, "runtime": {"lib/net9.0/OpenTelemetry.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}}, "OpenTelemetry.Api/1.12.0": {"type": "package", "dependencies": {"System.Diagnostics.DiagnosticSource": "9.0.0"}, "compile": {"lib/net9.0/OpenTelemetry.Api.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}, "runtime": {"lib/net9.0/OpenTelemetry.Api.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.12.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "OpenTelemetry.Api": "1.12.0"}, "compile": {"lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}, "runtime": {"lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}}, "OpenTelemetry.Exporter.OpenTelemetryProtocol/1.12.0": {"type": "package", "dependencies": {"OpenTelemetry": "1.12.0"}, "compile": {"lib/net9.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}, "runtime": {"lib/net9.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}}, "OpenTelemetry.Extensions.Hosting/1.12.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.0", "OpenTelemetry": "1.12.0"}, "compile": {"lib/net9.0/OpenTelemetry.Extensions.Hosting.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}, "runtime": {"lib/net9.0/OpenTelemetry.Extensions.Hosting.dll": {"related": ".dll-keyless.pem;.dll-keyless.sig;.xml"}}}, "OpenTelemetry.Instrumentation.AspNetCore/1.12.0": {"type": "package", "dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "[1.12.0, 2.0.0)"}, "compile": {"lib/net8.0/OpenTelemetry.Instrumentation.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "OpenTelemetry.Instrumentation.Http/1.12.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "OpenTelemetry.Api.ProviderBuilderExtensions": "[1.12.0, 2.0.0)"}, "compile": {"lib/net8.0/OpenTelemetry.Instrumentation.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.Http.dll": {"related": ".xml"}}}, "OpenTelemetry.Instrumentation.Runtime/1.12.0": {"type": "package", "dependencies": {"OpenTelemetry.Api": "[1.12.0, 2.0.0)"}, "compile": {"lib/net8.0/OpenTelemetry.Instrumentation.Runtime.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.Runtime.dll": {"related": ".xml"}}}, "Polly.Core/8.4.2": {"type": "package", "compile": {"lib/net8.0/Polly.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Polly.Core.dll": {"related": ".pdb;.xml"}}}, "Polly.Extensions/8.4.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Polly.Core": "8.4.2"}, "compile": {"lib/net8.0/Polly.Extensions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Polly.Extensions.dll": {"related": ".pdb;.xml"}}}, "Polly.RateLimiting/8.4.2": {"type": "package", "dependencies": {"Polly.Core": "8.4.2", "System.Threading.RateLimiting": "8.0.0"}, "compile": {"lib/net8.0/Polly.RateLimiting.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Polly.RateLimiting.dll": {"related": ".pdb;.xml"}}}, "System.Diagnostics.DiagnosticSource/9.0.0": {"type": "package", "compile": {"lib/net9.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.IO.Pipelines/9.0.4": {"type": "package", "compile": {"lib/net9.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Threading.RateLimiting/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Threading.RateLimiting.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Threading.RateLimiting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}}}, "libraries": {"Microsoft.Extensions.AmbientMetadata.Application/9.4.0": {"sha512": "zcjQBbp4whM4nxWmMg4P8JxTikB0dVxjKWHrGiz+6WVBgRctq9tSZgpbl/ywz06N+ZLzlooeWQOYgbpEOXOUYQ==", "type": "package", "path": "microsoft.extensions.ambientmetadata.application/9.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.AmbientMetadata.Application.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.AmbientMetadata.Application.dll", "lib/net462/Microsoft.Extensions.AmbientMetadata.Application.xml", "lib/net8.0/Microsoft.Extensions.AmbientMetadata.Application.dll", "lib/net8.0/Microsoft.Extensions.AmbientMetadata.Application.xml", "lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll", "lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.xml", "microsoft.extensions.ambientmetadata.application.9.4.0.nupkg.sha512", "microsoft.extensions.ambientmetadata.application.nuspec"]}, "Microsoft.Extensions.Compliance.Abstractions/9.4.0": {"sha512": "jDYE/o2Y/s9Tz896whtMEEUgCHPLSlzRXPRD6v2J1lJxnuuRcWIZ6ASJi6jSI4e9hbkpFTx8LtsL4CNB6bYbaQ==", "type": "package", "path": "microsoft.extensions.compliance.abstractions/9.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "lib/net8.0/Microsoft.Extensions.Compliance.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Compliance.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Compliance.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Compliance.Abstractions.xml", "microsoft.extensions.compliance.abstractions.9.4.0.nupkg.sha512", "microsoft.extensions.compliance.abstractions.nuspec"]}, "Microsoft.Extensions.Configuration/9.0.4": {"sha512": "KIVBrMbItnCJDd1RF4KEaE8jZwDJcDUJW5zXpbwQ05HNYTK1GveHxHK0B3SjgDJuR48GRACXAO+BLhL8h34S7g==", "type": "package", "path": "microsoft.extensions.configuration/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.9.0.4.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"sha512": "0LN/DiIKvBrkqp7gkF3qhGIeZk6/B63PthAHjQsxymJfIBcz0kbf4/p/t4lMgggVxZ+flRi5xvTwlpPOoZk8fg==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/9.0.4": {"sha512": "cdrjcl9RIcwt3ECbnpP0Gt1+pkjdW90mq5yFYy8D9qRj2NqFFcv3yDp141iEamsd9E218sGxK8WHaIOcrqgDJg==", "type": "package", "path": "microsoft.extensions.configuration.binder/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.9.0.4.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.4": {"sha512": "f2MTUaS2EQ3lX4325ytPAISZqgBfXmY0WvgD80ji6Z20AoDNiCESxsqo6mFRwHJD/jfVKRw9FsW6+86gNre3ug==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.4.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"sha512": "UI0TQPVkS78bFdjkTodmkH0Fe8lXv9LnhGFKgKrsgUJ5a5FVdFRcgjIkBVLbGgdRhxWirxH/8IXUtEyYJx6GQg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.4.0": {"sha512": "dg2VeBxiwqAszJkMRQT1k1jefeTneN+M8v/EkWwebR9OvLXzykP9CVE2+bWMuPvXIdHM04/ffWgo5Bl+0jKi/Q==", "type": "package", "path": "microsoft.extensions.dependencyinjection.autoactivation/9.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.DependencyInjection.AutoActivation.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.DependencyInjection.AutoActivation.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.AutoActivation.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.AutoActivation.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.xml", "microsoft.extensions.dependencyinjection.autoactivation.9.4.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.autoactivation.nuspec"]}, "Microsoft.Extensions.Diagnostics/9.0.4": {"sha512": "1bCSQrGv9+bpF5MGKF6THbnRFUZqQDrWPA39NDeVW9djeHBmow8kX4SX6/8KkeKI8gmUDG7jsG/bVuNAcY/ATQ==", "type": "package", "path": "microsoft.extensions.diagnostics/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.targets", "lib/net462/Microsoft.Extensions.Diagnostics.dll", "lib/net462/Microsoft.Extensions.Diagnostics.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.xml", "microsoft.extensions.diagnostics.9.0.4.nupkg.sha512", "microsoft.extensions.diagnostics.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.4": {"sha512": "IAucBcHYtiCmMyFag+Vrp5m+cjGRlDttJk9Vx7Dqpq+Ama4BzVUOk0JARQakgFFr7ZTBSgLKlHmtY5MiItB7Cg==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.4.0": {"sha512": "Ht5TQmWfYh7cbZTPTTWAUkgQmJhDg1JINvXSnxutx1sfZL1gkk1OXoF0Uh2383FLk9cm4le/cWfdgDslFeVigA==", "type": "package", "path": "microsoft.extensions.diagnostics.exceptionsummarization/9.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.Diagnostics.ExceptionSummarization.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll", "lib/net462/Microsoft.Extensions.Diagnostics.ExceptionSummarization.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.xml", "microsoft.extensions.diagnostics.exceptionsummarization.9.4.0.nupkg.sha512", "microsoft.extensions.diagnostics.exceptionsummarization.nuspec"]}, "Microsoft.Extensions.Features/8.0.15": {"sha512": "QUBxyUDGAyHppX5lDgE5XogfQ9fFtm3o4Un8cb46OMinto5hgYfKj3zZTR68QHAqKIM1iOoO6qrh1UbHUZkDQw==", "type": "package", "path": "microsoft.extensions.features/8.0.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Features.dll", "lib/net462/Microsoft.Extensions.Features.xml", "lib/net8.0/Microsoft.Extensions.Features.dll", "lib/net8.0/Microsoft.Extensions.Features.xml", "lib/netstandard2.0/Microsoft.Extensions.Features.dll", "lib/netstandard2.0/Microsoft.Extensions.Features.xml", "microsoft.extensions.features.8.0.15.nupkg.sha512", "microsoft.extensions.features.nuspec"]}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.4": {"sha512": "gQN2o/KnBfVk6Bd71E2YsvO5lsqrqHmaepDGk+FB/C4aiQY9B0XKKNKfl5/TqcNOs9OEithm4opiMHAErMFyEw==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/9.0.4": {"sha512": "bXkwRPMo4x19YKH6/V9XotU7KYQJlihXhcWO1RDclAY3yfY3XNg4QtSEBvng4kK/DnboE0O/nwSl+6Jiv9P+FA==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Http/9.0.4": {"sha512": "ezelU6HJgmq4862YoWuEbHGSV+JnfnonTSbNSJVh6n6wDehyiJn4hBtcK7rGbf2KO3QeSvK5y8E7uzn1oaRH5w==", "type": "package", "path": "microsoft.extensions.http/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Http.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Http.targets", "lib/net462/Microsoft.Extensions.Http.dll", "lib/net462/Microsoft.Extensions.Http.xml", "lib/net8.0/Microsoft.Extensions.Http.dll", "lib/net8.0/Microsoft.Extensions.Http.xml", "lib/net9.0/Microsoft.Extensions.Http.dll", "lib/net9.0/Microsoft.Extensions.Http.xml", "lib/netstandard2.0/Microsoft.Extensions.Http.dll", "lib/netstandard2.0/Microsoft.Extensions.Http.xml", "microsoft.extensions.http.9.0.4.nupkg.sha512", "microsoft.extensions.http.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Http.Diagnostics/9.4.0": {"sha512": "LaYz7FDkJ/uh5PYOTFvhQBndvgrtpjDSLiuDO2JGYcgA75uU7QXiMDHI/RmS4LzvJUkadPgXKxquTkhPotTw9Q==", "type": "package", "path": "microsoft.extensions.http.diagnostics/9.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.Http.Diagnostics.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.Http.Diagnostics.dll", "lib/net462/Microsoft.Extensions.Http.Diagnostics.xml", "lib/net8.0/Microsoft.Extensions.Http.Diagnostics.dll", "lib/net8.0/Microsoft.Extensions.Http.Diagnostics.xml", "lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll", "lib/net9.0/Microsoft.Extensions.Http.Diagnostics.xml", "microsoft.extensions.http.diagnostics.9.4.0.nupkg.sha512", "microsoft.extensions.http.diagnostics.nuspec"]}, "Microsoft.Extensions.Http.Resilience/9.4.0": {"sha512": "sml3XlyLrAnxYFTn96KaHzrzcnbkSZpxWzDffE9qs3QXloLepOa0nUjsgvhDg9InCNrd/KAuRLUzE6LCPa7r/g==", "type": "package", "path": "microsoft.extensions.http.resilience/9.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.Http.Resilience.net462.targets", "buildTransitive/net462/Microsoft.Extensions.Http.Resilience.targets", "buildTransitive/net8.0/Microsoft.Extensions.Http.Resilience.targets", "lib/net462/Microsoft.Extensions.Http.Resilience.dll", "lib/net462/Microsoft.Extensions.Http.Resilience.xml", "lib/net8.0/Microsoft.Extensions.Http.Resilience.dll", "lib/net8.0/Microsoft.Extensions.Http.Resilience.xml", "lib/net9.0/Microsoft.Extensions.Http.Resilience.dll", "lib/net9.0/Microsoft.Extensions.Http.Resilience.xml", "microsoft.extensions.http.resilience.9.4.0.nupkg.sha512", "microsoft.extensions.http.resilience.nuspec"]}, "Microsoft.Extensions.Logging/9.0.4": {"sha512": "xW6QPYsqhbuWBO9/1oA43g/XPKbohJx+7G8FLQgQXIriYvY7s+gxr2wjQJfRoPO900dvvv2vVH7wZovG+M1m6w==", "type": "package", "path": "microsoft.extensions.logging/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.4.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"sha512": "0MXlimU4Dud6t+iNi5NEz3dO2w1HXdhoOLaYFuLPCjAsvlPQGwOT6V2KZRMLEhCAm/stSZt1AUv0XmDdkjvtbw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Configuration/9.0.4": {"sha512": "/kF+rSnoo3/nIwGzWsR4RgBnoTOdZ3lzz2qFRyp/GgaNid4j6hOAQrs/O+QHXhlcAdZxjg37MvtIE+pAvIgi9g==", "type": "package", "path": "microsoft.extensions.logging.configuration/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Configuration.targets", "lib/net462/Microsoft.Extensions.Logging.Configuration.dll", "lib/net462/Microsoft.Extensions.Logging.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.xml", "microsoft.extensions.logging.configuration.9.0.4.nupkg.sha512", "microsoft.extensions.logging.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.ObjectPool/9.0.4": {"sha512": "G7p1k2xVZ+2aVANz0JdSiafr+AHDHeS1kF8+Y0ABbIsByd0erOL59IDXBs9vcdJf3pPV/murO0mbtr4k40QxWw==", "type": "package", "path": "microsoft.extensions.objectpool/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.ObjectPool.dll", "lib/net462/Microsoft.Extensions.ObjectPool.xml", "lib/net9.0/Microsoft.Extensions.ObjectPool.dll", "lib/net9.0/Microsoft.Extensions.ObjectPool.xml", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.xml", "microsoft.extensions.objectpool.9.0.4.nupkg.sha512", "microsoft.extensions.objectpool.nuspec"]}, "Microsoft.Extensions.Options/9.0.4": {"sha512": "fiFI2+58kicqVZyt/6obqoFwHiab7LC4FkQ3mmiBJ28Yy4fAvy2+v9MRnSvvlOO8chTOjKsdafFl/K9veCPo5g==", "type": "package", "path": "microsoft.extensions.options/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.4.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.4": {"sha512": "aridVhAT3Ep+vsirR1pzjaOw0Jwiob6dc73VFQn2XmDfBA2X98M8YKO1GarvsXRX7gX1Aj+hj2ijMzrMHDOm0A==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.9.0.4.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.4": {"sha512": "SPFyMjyku1nqTFFJ928JAMd0QnRe4xjE7KeKnZMWXf3xk+6e0WiOZAluYtLdbJUXtsl2cCRSi8cBquJ408k8RA==", "type": "package", "path": "microsoft.extensions.primitives/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.4.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Resilience/9.4.0": {"sha512": "Xb3zb0x+2O8jGJIysLTqD7Pd9uWDepPX8kUuojE0wZkGK5FztT0nxEHvYcmbICmRRNlFFDEd0dLgBWBPOSHWoA==", "type": "package", "path": "microsoft.extensions.resilience/9.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.Resilience.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.Resilience.dll", "lib/net462/Microsoft.Extensions.Resilience.xml", "lib/net8.0/Microsoft.Extensions.Resilience.dll", "lib/net8.0/Microsoft.Extensions.Resilience.xml", "lib/net9.0/Microsoft.Extensions.Resilience.dll", "lib/net9.0/Microsoft.Extensions.Resilience.xml", "microsoft.extensions.resilience.9.4.0.nupkg.sha512", "microsoft.extensions.resilience.nuspec"]}, "Microsoft.Extensions.ServiceDiscovery/9.3.1": {"sha512": "z9tKpMm4V5LIMABboRjU2w+hYme4EUigi/OaOBcJZfRLHON/e6rtlkShZ0Opl4ownuReyCbUFbH2sD2C57NWvA==", "type": "package", "path": "microsoft.extensions.servicediscovery/9.3.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.Extensions.ServiceDiscovery.dll", "lib/net8.0/Microsoft.Extensions.ServiceDiscovery.xml", "microsoft.extensions.servicediscovery.9.3.1.nupkg.sha512", "microsoft.extensions.servicediscovery.nuspec"]}, "Microsoft.Extensions.ServiceDiscovery.Abstractions/9.3.1": {"sha512": "qQFNxjqj3jgLsRUwQRs15PCpibaV3EWqnKZH+JJouDZzaCVlEpryWndJHzzy/FZ6upr5e7S+dJPTOa2CyKGuSg==", "type": "package", "path": "microsoft.extensions.servicediscovery.abstractions/9.3.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.Extensions.ServiceDiscovery.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.ServiceDiscovery.Abstractions.xml", "microsoft.extensions.servicediscovery.abstractions.9.3.1.nupkg.sha512", "microsoft.extensions.servicediscovery.abstractions.nuspec"]}, "Microsoft.Extensions.Telemetry/9.4.0": {"sha512": "Ax7GV4I2/BaiaAax+Yo1yjn2LYdw6FJP6gwCIcRMknu61iLZw8rZNx4tMz5JCGXj2ZO937NOwMcCrGN+wLB6NA==", "type": "package", "path": "microsoft.extensions.telemetry/9.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "buildTransitive/net462/Microsoft.Extensions.Telemetry.targets", "buildTransitive/net8.0/_._", "lib/net462/Microsoft.Extensions.Telemetry.dll", "lib/net462/Microsoft.Extensions.Telemetry.xml", "lib/net8.0/Microsoft.Extensions.Telemetry.dll", "lib/net8.0/Microsoft.Extensions.Telemetry.xml", "lib/net9.0/Microsoft.Extensions.Telemetry.dll", "lib/net9.0/Microsoft.Extensions.Telemetry.xml", "microsoft.extensions.telemetry.9.4.0.nupkg.sha512", "microsoft.extensions.telemetry.nuspec"]}, "Microsoft.Extensions.Telemetry.Abstractions/9.4.0": {"sha512": "QoXTK3gaW1f/1Ms2NDkKmAc+IW/yMsBTTqxphiGL4AfZNKoh8Ie7srDOaMuhInm5/Qq0xEAkIafC53v1xt7wMQ==", "type": "package", "path": "microsoft.extensions.telemetry.abstractions/9.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "analyzers/dotnet/cs/Microsoft.Gen.Logging.dll", "analyzers/dotnet/cs/Microsoft.Gen.Metrics.dll", "buildTransitive/net462/Microsoft.Extensions.Telemetry.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Telemetry.Abstractions.props", "buildTransitive/net6.0/Microsoft.Extensions.Telemetry.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Telemetry.Abstractions.props", "buildTransitive/net8.0/Microsoft.Extensions.Telemetry.Abstractions.targets", "lib/net462/Microsoft.Extensions.Telemetry.Abstractions.dll", "lib/net462/Microsoft.Extensions.Telemetry.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Telemetry.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Telemetry.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.xml", "microsoft.extensions.telemetry.abstractions.9.4.0.nupkg.sha512", "microsoft.extensions.telemetry.abstractions.nuspec"]}, "OpenTelemetry/1.12.0": {"sha512": "aIEu2O3xFOdwIVH0AJsIHPIMH1YuX18nzu7BHyaDNQ6NWSk4Zyrs9Pp6y8SATuSbvdtmvue4mj/QZ3838srbwA==", "type": "package", "path": "opentelemetry/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net462/OpenTelemetry.dll", "lib/net462/OpenTelemetry.dll-keyless.pem", "lib/net462/OpenTelemetry.dll-keyless.sig", "lib/net462/OpenTelemetry.xml", "lib/net8.0/OpenTelemetry.dll", "lib/net8.0/OpenTelemetry.dll-keyless.pem", "lib/net8.0/OpenTelemetry.dll-keyless.sig", "lib/net8.0/OpenTelemetry.xml", "lib/net9.0/OpenTelemetry.dll", "lib/net9.0/OpenTelemetry.dll-keyless.pem", "lib/net9.0/OpenTelemetry.dll-keyless.sig", "lib/net9.0/OpenTelemetry.xml", "lib/netstandard2.0/OpenTelemetry.dll", "lib/netstandard2.0/OpenTelemetry.dll-keyless.pem", "lib/netstandard2.0/OpenTelemetry.dll-keyless.sig", "lib/netstandard2.0/OpenTelemetry.xml", "lib/netstandard2.1/OpenTelemetry.dll", "lib/netstandard2.1/OpenTelemetry.dll-keyless.pem", "lib/netstandard2.1/OpenTelemetry.dll-keyless.sig", "lib/netstandard2.1/OpenTelemetry.xml", "opentelemetry-icon-color.png", "opentelemetry.1.12.0.nupkg.sha512", "opentelemetry.nuspec"]}, "OpenTelemetry.Api/1.12.0": {"sha512": "Xt0qldi+iE2szGrM3jAqzEMEJd48YBtqI6mge0+ArXTZg3aTpRmyhL6CKKl3bLioaFSSVbBpEbPin8u6Z46Yrw==", "type": "package", "path": "opentelemetry.api/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net462/OpenTelemetry.Api.dll", "lib/net462/OpenTelemetry.Api.dll-keyless.pem", "lib/net462/OpenTelemetry.Api.dll-keyless.sig", "lib/net462/OpenTelemetry.Api.xml", "lib/net8.0/OpenTelemetry.Api.dll", "lib/net8.0/OpenTelemetry.Api.dll-keyless.pem", "lib/net8.0/OpenTelemetry.Api.dll-keyless.sig", "lib/net8.0/OpenTelemetry.Api.xml", "lib/net9.0/OpenTelemetry.Api.dll", "lib/net9.0/OpenTelemetry.Api.dll-keyless.pem", "lib/net9.0/OpenTelemetry.Api.dll-keyless.sig", "lib/net9.0/OpenTelemetry.Api.xml", "lib/netstandard2.0/OpenTelemetry.Api.dll", "lib/netstandard2.0/OpenTelemetry.Api.dll-keyless.pem", "lib/netstandard2.0/OpenTelemetry.Api.dll-keyless.sig", "lib/netstandard2.0/OpenTelemetry.Api.xml", "opentelemetry-icon-color.png", "opentelemetry.api.1.12.0.nupkg.sha512", "opentelemetry.api.nuspec"]}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.12.0": {"sha512": "t6Vk1143BfiisCWYbRcyzkAuN6Aq5RkYtfOSMoqCIRMvtN9p1e1xzc0nWQ+fccNGOVgHn3aMK5xFn2+iWMcr8A==", "type": "package", "path": "opentelemetry.api.providerbuilderextensions/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net462/OpenTelemetry.Api.ProviderBuilderExtensions.dll", "lib/net462/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.pem", "lib/net462/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.sig", "lib/net462/OpenTelemetry.Api.ProviderBuilderExtensions.xml", "lib/net8.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll", "lib/net8.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.pem", "lib/net8.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.sig", "lib/net8.0/OpenTelemetry.Api.ProviderBuilderExtensions.xml", "lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll", "lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.pem", "lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.sig", "lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.xml", "lib/netstandard2.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll", "lib/netstandard2.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.pem", "lib/netstandard2.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll-keyless.sig", "lib/netstandard2.0/OpenTelemetry.Api.ProviderBuilderExtensions.xml", "opentelemetry-icon-color.png", "opentelemetry.api.providerbuilderextensions.1.12.0.nupkg.sha512", "opentelemetry.api.providerbuilderextensions.nuspec"]}, "OpenTelemetry.Exporter.OpenTelemetryProtocol/1.12.0": {"sha512": "7LzQSPhz5pNaL4xZgT3wkZODA1NLrEq3bet8KDHgtaJ9q+VNP7wmiZky8gQfMkB4FXuI/pevT8ZurL4p5997WA==", "type": "package", "path": "opentelemetry.exporter.opentelemetryprotocol/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net462/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll", "lib/net462/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.pem", "lib/net462/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.sig", "lib/net462/OpenTelemetry.Exporter.OpenTelemetryProtocol.xml", "lib/net8.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll", "lib/net8.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.pem", "lib/net8.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.sig", "lib/net8.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.xml", "lib/net9.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll", "lib/net9.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.pem", "lib/net9.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.sig", "lib/net9.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.xml", "lib/netstandard2.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll", "lib/netstandard2.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.pem", "lib/netstandard2.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.sig", "lib/netstandard2.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.xml", "lib/netstandard2.1/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll", "lib/netstandard2.1/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.pem", "lib/netstandard2.1/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll-keyless.sig", "lib/netstandard2.1/OpenTelemetry.Exporter.OpenTelemetryProtocol.xml", "opentelemetry-icon-color.png", "opentelemetry.exporter.opentelemetryprotocol.1.12.0.nupkg.sha512", "opentelemetry.exporter.opentelemetryprotocol.nuspec"]}, "OpenTelemetry.Extensions.Hosting/1.12.0": {"sha512": "6/8O6rsJRwslg5/Fm3bscBelw4Yh9T9CN24p7cAsuEFkrmmeSO9gkYUCK02Qi+CmPM2KHYTLjKi0lJaCsDMWQA==", "type": "package", "path": "opentelemetry.extensions.hosting/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net462/OpenTelemetry.Extensions.Hosting.dll", "lib/net462/OpenTelemetry.Extensions.Hosting.dll-keyless.pem", "lib/net462/OpenTelemetry.Extensions.Hosting.dll-keyless.sig", "lib/net462/OpenTelemetry.Extensions.Hosting.xml", "lib/net8.0/OpenTelemetry.Extensions.Hosting.dll", "lib/net8.0/OpenTelemetry.Extensions.Hosting.dll-keyless.pem", "lib/net8.0/OpenTelemetry.Extensions.Hosting.dll-keyless.sig", "lib/net8.0/OpenTelemetry.Extensions.Hosting.xml", "lib/net9.0/OpenTelemetry.Extensions.Hosting.dll", "lib/net9.0/OpenTelemetry.Extensions.Hosting.dll-keyless.pem", "lib/net9.0/OpenTelemetry.Extensions.Hosting.dll-keyless.sig", "lib/net9.0/OpenTelemetry.Extensions.Hosting.xml", "lib/netstandard2.0/OpenTelemetry.Extensions.Hosting.dll", "lib/netstandard2.0/OpenTelemetry.Extensions.Hosting.dll-keyless.pem", "lib/netstandard2.0/OpenTelemetry.Extensions.Hosting.dll-keyless.sig", "lib/netstandard2.0/OpenTelemetry.Extensions.Hosting.xml", "opentelemetry-icon-color.png", "opentelemetry.extensions.hosting.1.12.0.nupkg.sha512", "opentelemetry.extensions.hosting.nuspec"]}, "OpenTelemetry.Instrumentation.AspNetCore/1.12.0": {"sha512": "r+Mzggd2P4N0Y34QIO6kakVPBOKFYSHnLkTrXXM+r37ABp+iaUvVUe+u/uxszsi5f7P5mrG0uYYaJ1QGHvzo3A==", "type": "package", "path": "opentelemetry.instrumentation.aspnetcore/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "lib/net8.0/OpenTelemetry.Instrumentation.AspNetCore.dll", "lib/net8.0/OpenTelemetry.Instrumentation.AspNetCore.xml", "lib/netstandard2.0/OpenTelemetry.Instrumentation.AspNetCore.dll", "lib/netstandard2.0/OpenTelemetry.Instrumentation.AspNetCore.xml", "opentelemetry-icon-color.png", "opentelemetry.instrumentation.aspnetcore.1.12.0.nupkg.sha512", "opentelemetry.instrumentation.aspnetcore.nuspec"]}, "OpenTelemetry.Instrumentation.Http/1.12.0": {"sha512": "0rW+MbHgUQAdbvBtRxPYoQBosbNdWegL7cYkRlxq+KQ/VFyU8itt4pWTccmu1/FWmTgqJyT3LaujyDZoRrm8Yg==", "type": "package", "path": "opentelemetry.instrumentation.http/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "lib/net462/OpenTelemetry.Instrumentation.Http.dll", "lib/net462/OpenTelemetry.Instrumentation.Http.xml", "lib/net8.0/OpenTelemetry.Instrumentation.Http.dll", "lib/net8.0/OpenTelemetry.Instrumentation.Http.xml", "lib/netstandard2.0/OpenTelemetry.Instrumentation.Http.dll", "lib/netstandard2.0/OpenTelemetry.Instrumentation.Http.xml", "opentelemetry-icon-color.png", "opentelemetry.instrumentation.http.1.12.0.nupkg.sha512", "opentelemetry.instrumentation.http.nuspec"]}, "OpenTelemetry.Instrumentation.Runtime/1.12.0": {"sha512": "xmd0TAm2x+T3ztdf5BolIwLPh+Uy6osaBeIQtCXv611PN7h/Pnhsjg5lU2hkAWj7M7ns74U5wtVpS8DXmJ+94w==", "type": "package", "path": "opentelemetry.instrumentation.runtime/1.12.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "lib/net462/OpenTelemetry.Instrumentation.Runtime.dll", "lib/net462/OpenTelemetry.Instrumentation.Runtime.xml", "lib/net8.0/OpenTelemetry.Instrumentation.Runtime.dll", "lib/net8.0/OpenTelemetry.Instrumentation.Runtime.xml", "lib/netstandard2.0/OpenTelemetry.Instrumentation.Runtime.dll", "lib/netstandard2.0/OpenTelemetry.Instrumentation.Runtime.xml", "opentelemetry-icon-color.png", "opentelemetry.instrumentation.runtime.1.12.0.nupkg.sha512", "opentelemetry.instrumentation.runtime.nuspec"]}, "Polly.Core/8.4.2": {"sha512": "BpE2I6HBYYA5tF0Vn4eoQOGYTYIK1BlF5EXVgkWGn3mqUUjbXAr13J6fZVbp7Q3epRR8yshacBMlsHMhpOiV3g==", "type": "package", "path": "polly.core/8.4.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Polly.Core.dll", "lib/net462/Polly.Core.pdb", "lib/net462/Polly.Core.xml", "lib/net472/Polly.Core.dll", "lib/net472/Polly.Core.pdb", "lib/net472/Polly.Core.xml", "lib/net6.0/Polly.Core.dll", "lib/net6.0/Polly.Core.pdb", "lib/net6.0/Polly.Core.xml", "lib/net8.0/Polly.Core.dll", "lib/net8.0/Polly.Core.pdb", "lib/net8.0/Polly.Core.xml", "lib/netstandard2.0/Polly.Core.dll", "lib/netstandard2.0/Polly.Core.pdb", "lib/netstandard2.0/Polly.Core.xml", "package-icon.png", "package-readme.md", "polly.core.8.4.2.nupkg.sha512", "polly.core.nuspec"]}, "Polly.Extensions/8.4.2": {"sha512": "GZ9vRVmR0jV2JtZavt+pGUsQ1O1cuRKG7R7VOZI6ZDy9y6RNPvRvXK1tuS4ffUrv8L0FTea59oEuQzgS0R7zSA==", "type": "package", "path": "polly.extensions/8.4.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Polly.Extensions.dll", "lib/net462/Polly.Extensions.pdb", "lib/net462/Polly.Extensions.xml", "lib/net472/Polly.Extensions.dll", "lib/net472/Polly.Extensions.pdb", "lib/net472/Polly.Extensions.xml", "lib/net6.0/Polly.Extensions.dll", "lib/net6.0/Polly.Extensions.pdb", "lib/net6.0/Polly.Extensions.xml", "lib/net8.0/Polly.Extensions.dll", "lib/net8.0/Polly.Extensions.pdb", "lib/net8.0/Polly.Extensions.xml", "lib/netstandard2.0/Polly.Extensions.dll", "lib/netstandard2.0/Polly.Extensions.pdb", "lib/netstandard2.0/Polly.Extensions.xml", "package-icon.png", "package-readme.md", "polly.extensions.8.4.2.nupkg.sha512", "polly.extensions.nuspec"]}, "Polly.RateLimiting/8.4.2": {"sha512": "ehTImQ/eUyO07VYW2WvwSmU9rRH200SKJ/3jku9rOkyWE0A2JxNFmAVms8dSn49QLSjmjFRRSgfNyOgr/2PSmA==", "type": "package", "path": "polly.ratelimiting/8.4.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Polly.RateLimiting.dll", "lib/net462/Polly.RateLimiting.pdb", "lib/net462/Polly.RateLimiting.xml", "lib/net472/Polly.RateLimiting.dll", "lib/net472/Polly.RateLimiting.pdb", "lib/net472/Polly.RateLimiting.xml", "lib/net6.0/Polly.RateLimiting.dll", "lib/net6.0/Polly.RateLimiting.pdb", "lib/net6.0/Polly.RateLimiting.xml", "lib/net8.0/Polly.RateLimiting.dll", "lib/net8.0/Polly.RateLimiting.pdb", "lib/net8.0/Polly.RateLimiting.xml", "lib/netstandard2.0/Polly.RateLimiting.dll", "lib/netstandard2.0/Polly.RateLimiting.pdb", "lib/netstandard2.0/Polly.RateLimiting.xml", "package-icon.png", "package-readme.md", "polly.ratelimiting.8.4.2.nupkg.sha512", "polly.ratelimiting.nuspec"]}, "System.Diagnostics.DiagnosticSource/9.0.0": {"sha512": "ddppcFpnbohLWdYKr/ZeLZHmmI+DXFgZ3Snq+/E7SwcdW4UnvxmaugkwGywvGVWkHPGCSZjCP+MLzu23AL5SDw==", "type": "package", "path": "system.diagnostics.diagnosticsource/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "content/ILLink/ILLink.Descriptors.LibraryBuild.xml", "contentFiles/any/net462/ILLink/ILLink.Descriptors.LibraryBuild.xml", "contentFiles/any/net8.0/ILLink/ILLink.Descriptors.LibraryBuild.xml", "contentFiles/any/net9.0/ILLink/ILLink.Descriptors.LibraryBuild.xml", "contentFiles/any/netstandard2.0/ILLink/ILLink.Descriptors.LibraryBuild.xml", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/net9.0/System.Diagnostics.DiagnosticSource.dll", "lib/net9.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.9.0.0.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Pipelines/9.0.4": {"sha512": "luF2Xba+lTe2GOoNQdZLe8q7K6s7nSpWZl9jIwWNMszN4/Yv0lmxk9HISgMmwdyZ83i3UhAGXaSY9o6IJBUuuA==", "type": "package", "path": "system.io.pipelines/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net8.0/System.IO.Pipelines.dll", "lib/net8.0/System.IO.Pipelines.xml", "lib/net9.0/System.IO.Pipelines.dll", "lib/net9.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.9.0.4.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.RateLimiting/8.0.0": {"sha512": "7mu9v0QDv66ar3DpGSZHg9NuNcxDaaAcnMULuZlaTpP9+hwXhrxNGsF5GmLkSHxFdb5bBc1TzeujsRgTrPWi+Q==", "type": "package", "path": "system.threading.ratelimiting/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Threading.RateLimiting.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Threading.RateLimiting.targets", "lib/net462/System.Threading.RateLimiting.dll", "lib/net462/System.Threading.RateLimiting.xml", "lib/net6.0/System.Threading.RateLimiting.dll", "lib/net6.0/System.Threading.RateLimiting.xml", "lib/net7.0/System.Threading.RateLimiting.dll", "lib/net7.0/System.Threading.RateLimiting.xml", "lib/net8.0/System.Threading.RateLimiting.dll", "lib/net8.0/System.Threading.RateLimiting.xml", "lib/netstandard2.0/System.Threading.RateLimiting.dll", "lib/netstandard2.0/System.Threading.RateLimiting.xml", "system.threading.ratelimiting.8.0.0.nupkg.sha512", "system.threading.ratelimiting.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net9.0": ["Microsoft.Extensions.Http.Resilience >= 9.4.0", "Microsoft.Extensions.ServiceDiscovery >= 9.3.1", "OpenTelemetry.Exporter.OpenTelemetryProtocol >= 1.12.0", "OpenTelemetry.Extensions.Hosting >= 1.12.0", "OpenTelemetry.Instrumentation.AspNetCore >= 1.12.0", "OpenTelemetry.Instrumentation.Http >= 1.12.0", "OpenTelemetry.Instrumentation.Runtime >= 1.12.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\_\\<PERSON>_G<PERSON>ub\\Miller.WMS\\Miller.WMS.ServiceDefaults\\Miller.WMS.ServiceDefaults.csproj", "projectName": "Miller.WMS.ServiceDefaults", "projectPath": "C:\\_\\<PERSON>_G<PERSON>ub\\Miller.WMS\\Miller.WMS.ServiceDefaults\\Miller.WMS.ServiceDefaults.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\_\\<PERSON>_<PERSON>ub\\Miller.WMS\\Miller.WMS.ServiceDefaults\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Http.Resilience": {"target": "Package", "version": "[9.4.0, )"}, "Microsoft.Extensions.ServiceDiscovery": {"target": "Package", "version": "[9.3.1, )"}, "OpenTelemetry.Exporter.OpenTelemetryProtocol": {"target": "Package", "version": "[1.12.0, )"}, "OpenTelemetry.Extensions.Hosting": {"target": "Package", "version": "[1.12.0, )"}, "OpenTelemetry.Instrumentation.AspNetCore": {"target": "Package", "version": "[1.12.0, )"}, "OpenTelemetry.Instrumentation.Http": {"target": "Package", "version": "[1.12.0, )"}, "OpenTelemetry.Instrumentation.Runtime": {"target": "Package", "version": "[1.12.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}