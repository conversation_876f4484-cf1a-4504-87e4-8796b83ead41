﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.testing.platform.msbuild\1.6.2\buildTransitive\Microsoft.Testing.Platform.MSBuild.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.testing.platform.msbuild\1.6.2\buildTransitive\Microsoft.Testing.Platform.MSBuild.targets')" />
    <Import Project="$(NuGetPackageRoot)xunit.v3.core\2.0.0\buildTransitive\xunit.v3.core.targets" Condition="Exists('$(NuGetPackageRoot)xunit.v3.core\2.0.0\buildTransitive\xunit.v3.core.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.4\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.4\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.options\9.0.4\buildTransitive\net8.0\Microsoft.Extensions.Options.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.options\9.0.4\buildTransitive\net8.0\Microsoft.Extensions.Options.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.binder\9.0.4\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.binder\9.0.4\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\9.0.4\buildTransitive\net8.0\Microsoft.Extensions.Configuration.UserSecrets.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\9.0.4\buildTransitive\net8.0\Microsoft.Extensions.Configuration.UserSecrets.targets')" />
    <Import Project="$(NuGetPackageRoot)aspire.hosting.orchestration.win-x64\9.3.1\buildTransitive\Aspire.Hosting.Orchestration.win-x64.targets" Condition="Exists('$(NuGetPackageRoot)aspire.hosting.orchestration.win-x64\9.3.1\buildTransitive\Aspire.Hosting.Orchestration.win-x64.targets')" />
    <Import Project="$(NuGetPackageRoot)aspire.dashboard.sdk.win-x64\9.3.1\buildTransitive\Aspire.Dashboard.Sdk.win-x64.targets" Condition="Exists('$(NuGetPackageRoot)aspire.dashboard.sdk.win-x64\9.3.1\buildTransitive\Aspire.Dashboard.Sdk.win-x64.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.codecoverage\17.10.0\build\netstandard2.0\Microsoft.CodeCoverage.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.codecoverage\17.10.0\build\netstandard2.0\Microsoft.CodeCoverage.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.net.test.sdk\17.10.0\build\netcoreapp3.1\Microsoft.NET.Test.Sdk.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.net.test.sdk\17.10.0\build\netcoreapp3.1\Microsoft.NET.Test.Sdk.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.telemetry.abstractions\9.4.0\buildTransitive\net8.0\Microsoft.Extensions.Telemetry.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.telemetry.abstractions\9.4.0\buildTransitive\net8.0\Microsoft.Extensions.Telemetry.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.http.resilience\9.4.0\buildTransitive\net8.0\Microsoft.Extensions.Http.Resilience.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.http.resilience\9.4.0\buildTransitive\net8.0\Microsoft.Extensions.Http.Resilience.targets')" />
    <Import Project="$(NuGetPackageRoot)coverlet.collector\6.0.2\build\netstandard2.0\coverlet.collector.targets" Condition="Exists('$(NuGetPackageRoot)coverlet.collector\6.0.2\build\netstandard2.0\coverlet.collector.targets')" />
  </ImportGroup>
</Project>