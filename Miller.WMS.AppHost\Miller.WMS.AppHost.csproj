<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.3.1" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UserSecretsId>0fb3be66-4f00-43d0-8571-b1e1d864aed3</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Miller.WMS.ApiService\Miller.WMS.ApiService.csproj" />
    <ProjectReference Include="..\Miller.WMS.Web\Miller.WMS.Web.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" Version="9.3.1" />
    <PackageReference Include="Aspire.Hosting.Redis" Version="9.3.1" />
  </ItemGroup>

</Project>
