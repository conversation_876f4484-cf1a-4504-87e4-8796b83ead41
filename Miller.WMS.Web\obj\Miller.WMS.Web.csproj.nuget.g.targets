﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.4\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.4\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.options\9.0.4\buildTransitive\net8.0\Microsoft.Extensions.Options.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.options\9.0.4\buildTransitive\net8.0\Microsoft.Extensions.Options.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.binder\9.0.4\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.binder\9.0.4\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.telemetry.abstractions\9.4.0\buildTransitive\net8.0\Microsoft.Extensions.Telemetry.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.telemetry.abstractions\9.4.0\buildTransitive\net8.0\Microsoft.Extensions.Telemetry.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.http.resilience\9.4.0\buildTransitive\net8.0\Microsoft.Extensions.Http.Resilience.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.http.resilience\9.4.0\buildTransitive\net8.0\Microsoft.Extensions.Http.Resilience.targets')" />
    <Import Project="$(NuGetPackageRoot)aspire.stackexchange.redis\9.3.1\buildTransitive\net8.0\Aspire.StackExchange.Redis.targets" Condition="Exists('$(NuGetPackageRoot)aspire.stackexchange.redis\9.3.1\buildTransitive\net8.0\Aspire.StackExchange.Redis.targets')" />
    <Import Project="$(NuGetPackageRoot)aspire.stackexchange.redis.outputcaching\9.3.1\buildTransitive\net8.0\Aspire.StackExchange.Redis.OutputCaching.targets" Condition="Exists('$(NuGetPackageRoot)aspire.stackexchange.redis.outputcaching\9.3.1\buildTransitive\net8.0\Aspire.StackExchange.Redis.OutputCaching.targets')" />
  </ImportGroup>
</Project>